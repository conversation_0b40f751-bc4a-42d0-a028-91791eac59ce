/**
 * Configuration for the notification system
 */

import { NotificationServiceConfig } from './types';

// Notification server configuration
export const NOTIFICATION_CONFIG: NotificationServiceConfig = {
  // Default to localhost for development, can be overridden via environment variables
  serverUrl: process.env.NEXT_PUBLIC_NOTIFICATION_SERVER_URL || 'http://localhost:8001',
  vapidPublicKey: '', // Will be fetched from server
  swPath: '/sw.js',
};

// API endpoints
export const API_ENDPOINTS = {
  VAPID_KEY: '/api/vapid-public-key',
  SUBSCRIBE: '/api/subscribe',
  UNSUBSCRIBE: '/api/unsubscribe',
  SEND_NOTIFICATION: '/api/notifications/send',
  STATS: '/api/stats',
} as const;

// Default notification options
export const DEFAULT_NOTIFICATION_OPTIONS = {
  icon: '/favicon.png',
  badge: '/favicon.png',
  requireInteraction: false,
  silent: false,
  renotify: false,
} as const;

// Service worker configuration
export const SW_CONFIG = {
  scope: '/',
  updateViaCache: 'none' as ServiceWorkerUpdateViaCache,
} as const;

// Permission request configuration
export const PERMISSION_CONFIG = {
  autoRequest: true, // Automatically request permission on page load
  showUI: true, // Show permission request UI
  retryDelay: 5000, // Delay before retrying failed operations (ms)
  maxRetries: 3, // Maximum number of retry attempts
} as const;

// Notification display configuration
export const DISPLAY_CONFIG = {
  defaultTitle: 'GamyDay',
  defaultIcon: '/favicon.png',
  defaultBadge: '/favicon.png',
  defaultUrl: '/',
  clickAction: 'focus_last_focused_or_open',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  PERMISSION_REQUESTED: 'gamyday_notification_permission_requested',
  SUBSCRIPTION_DATA: 'gamyday_notification_subscription',
  LAST_PERMISSION_REQUEST: 'gamyday_last_permission_request',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  NOT_SUPPORTED: 'Push notifications are not supported in this browser',
  PERMISSION_DENIED: 'Notification permission was denied',
  SUBSCRIPTION_FAILED: 'Failed to create push subscription',
  SERVER_ERROR: 'Failed to communicate with notification server',
  SW_REGISTRATION_FAILED: 'Failed to register service worker',
  VAPID_KEY_MISSING: 'VAPID public key not available',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  PERMISSION_GRANTED: 'Notification permission granted',
  SUBSCRIBED: 'Successfully subscribed to notifications',
  UNSUBSCRIBED: 'Successfully unsubscribed from notifications',
} as const;
