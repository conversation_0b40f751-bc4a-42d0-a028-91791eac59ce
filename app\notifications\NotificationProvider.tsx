'use client';

/**
 * Notification Context Provider
 * Manages notification state and provides notification functionality throughout the app
 */

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { notificationService } from './service';
import {
  NotificationContextType,
  NotificationPermissionState,
  ServiceWorkerMessage,
} from './types';
import { PERMISSION_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES } from './config';

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const [permissionState, setPermissionState] = useState<NotificationPermissionState>({
    permission: 'default',
    isSupported: false,
    isSubscribed: false,
    subscription: null,
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Update permission state
   */
  const updatePermissionState = useCallback(async () => {
    try {
      const state = notificationService.getPermissionState();
      
      if (state.isSupported) {
        const isSubscribed = await notificationService.isSubscribed();
        const subscription = await notificationService.getSubscription();
        
        setPermissionState({
          ...state,
          isSubscribed,
          subscription,
        });
      } else {
        setPermissionState(state);
      }
    } catch (error) {
      console.error('Failed to update permission state:', error);
    }
  }, []);

  /**
   * Request notification permission
   */
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!permissionState.isSupported) {
      setError(ERROR_MESSAGES.NOT_SUPPORTED);
      return false;
    }

    if (permissionState.permission === 'granted') {
      return true;
    }

    setIsLoading(true);
    setError(null);

    try {
      const granted = await notificationService.requestPermission();
      
      if (granted) {
        await updatePermissionState();
        console.log(SUCCESS_MESSAGES.PERMISSION_GRANTED);
      }
      
      return granted;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.PERMISSION_DENIED;
      setError(errorMessage);
      console.error('Permission request failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [permissionState.isSupported, permissionState.permission, updatePermissionState]);

  /**
   * Subscribe to push notifications
   */
  const subscribe = useCallback(async (): Promise<boolean> => {
    if (!permissionState.isSupported) {
      setError(ERROR_MESSAGES.NOT_SUPPORTED);
      return false;
    }

    if (permissionState.permission !== 'granted') {
      setError(ERROR_MESSAGES.PERMISSION_DENIED);
      return false;
    }

    if (permissionState.isSubscribed) {
      return true;
    }

    setIsLoading(true);
    setError(null);

    try {
      await notificationService.subscribe();
      await updatePermissionState();
      console.log(SUCCESS_MESSAGES.SUBSCRIBED);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.SUBSCRIPTION_FAILED;
      setError(errorMessage);
      console.error('Subscription failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [permissionState.isSupported, permissionState.permission, permissionState.isSubscribed, updatePermissionState]);

  /**
   * Unsubscribe from push notifications
   */
  const unsubscribe = useCallback(async (): Promise<boolean> => {
    if (!permissionState.isSubscribed) {
      return true;
    }

    setIsLoading(true);
    setError(null);

    try {
      await notificationService.unsubscribe();
      await updatePermissionState();
      console.log(SUCCESS_MESSAGES.UNSUBSCRIBED);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unsubscribe';
      setError(errorMessage);
      console.error('Unsubscribe failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [permissionState.isSubscribed, updatePermissionState]);

  /**
   * Handle service worker messages
   */
  const handleServiceWorkerMessage = useCallback((event: MessageEvent<ServiceWorkerMessage>) => {
    console.log('Service worker message received in context:', event.data);
    
    switch (event.data.type) {
      case 'SUBSCRIPTION_UPDATE':
        updatePermissionState();
        break;
      default:
        // Other message types are handled by the service
        break;
    }
  }, [updatePermissionState]);

  /**
   * Initialize notification service
   */
  const initializeService = useCallback(async () => {
    if (!notificationService.isSupported()) {
      setPermissionState(prev => ({ ...prev, isSupported: false }));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await notificationService.initialize();
      await updatePermissionState();
      
      // Set up service worker message listener
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
      }
      
      console.log('Notification service initialized');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize notifications';
      setError(errorMessage);
      console.error('Failed to initialize notification service:', error);
    } finally {
      setIsLoading(false);
    }
  }, [updatePermissionState, handleServiceWorkerMessage]);

  /**
   * Auto-request permission if configured
   */
  const autoRequestPermission = useCallback(async () => {
    if (notificationService.shouldAutoRequestPermission()) {
      console.log('Auto-requesting notification permission');
      
      // Add a small delay to avoid interrupting page load
      setTimeout(async () => {
        try {
          const granted = await requestPermission();
          if (granted) {
            // Auto-subscribe if permission granted
            await subscribe();
          }
        } catch (error) {
          console.log('Auto permission request failed:', error);
          // Don't show error for auto-request failures
        }
      }, 2000);
    }
  }, [requestPermission, subscribe]);

  /**
   * Initialize on mount
   */
  useEffect(() => {
    initializeService();
  }, [initializeService]);

  /**
   * Auto-request permission after initialization
   */
  useEffect(() => {
    if (permissionState.isSupported && !isLoading && !error) {
      autoRequestPermission();
    }
  }, [permissionState.isSupported, isLoading, error, autoRequestPermission]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      }
    };
  }, [handleServiceWorkerMessage]);

  const contextValue: NotificationContextType = {
    permissionState,
    requestPermission,
    subscribe,
    unsubscribe,
    isLoading,
    error,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

/**
 * Hook to use notification context
 */
export function useNotifications(): NotificationContextType {
  const context = useContext(NotificationContext);
  
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  
  return context;
}
