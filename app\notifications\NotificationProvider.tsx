'use client';

/**
 * Notification Context Provider
 * Manages notification state and provides notification functionality throughout the app
 */

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { notificationService } from './service';
import {
  NotificationContextType,
  NotificationPermissionState,
  ServiceWorkerMessage,
} from './types';
import { PERMISSION_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES } from './config';

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  console.log('[NotificationProvider] Component rendered');

  const [permissionState, setPermissionState] = useState<NotificationPermissionState>({
    permission: 'default',
    isSupported: false,
    isSubscribed: false,
    subscription: null,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Update permission state
   */
  const updatePermissionState = useCallback(async () => {
    try {
      const state = notificationService.getPermissionState();
      
      if (state.isSupported) {
        const isSubscribed = await notificationService.isSubscribed();
        const subscription = await notificationService.getSubscription();
        
        setPermissionState({
          ...state,
          isSubscribed,
          subscription,
        });
      } else {
        setPermissionState(state);
      }
    } catch (error) {
      console.error('Failed to update permission state:', error);
    }
  }, []);

  /**
   * Request notification permission
   */
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!permissionState.isSupported) {
      setError(ERROR_MESSAGES.NOT_SUPPORTED);
      return false;
    }

    if (permissionState.permission === 'granted') {
      return true;
    }

    setIsLoading(true);
    setError(null);

    try {
      const granted = await notificationService.requestPermission();
      
      if (granted) {
        await updatePermissionState();
        console.log(SUCCESS_MESSAGES.PERMISSION_GRANTED);
      }
      
      return granted;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.PERMISSION_DENIED;
      setError(errorMessage);
      console.error('Permission request failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [permissionState.isSupported, permissionState.permission, updatePermissionState]);

  /**
   * Subscribe to push notifications
   */
  const subscribe = useCallback(async (): Promise<boolean> => {
    if (!permissionState.isSupported) {
      setError(ERROR_MESSAGES.NOT_SUPPORTED);
      return false;
    }

    if (permissionState.permission !== 'granted') {
      setError(ERROR_MESSAGES.PERMISSION_DENIED);
      return false;
    }

    if (permissionState.isSubscribed) {
      return true;
    }

    setIsLoading(true);
    setError(null);

    try {
      await notificationService.subscribe();
      await updatePermissionState();
      console.log(SUCCESS_MESSAGES.SUBSCRIBED);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.SUBSCRIPTION_FAILED;
      setError(errorMessage);
      console.error('Subscription failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [permissionState.isSupported, permissionState.permission, permissionState.isSubscribed, updatePermissionState]);

  /**
   * Unsubscribe from push notifications
   */
  const unsubscribe = useCallback(async (): Promise<boolean> => {
    if (!permissionState.isSubscribed) {
      return true;
    }

    setIsLoading(true);
    setError(null);

    try {
      await notificationService.unsubscribe();
      await updatePermissionState();
      console.log(SUCCESS_MESSAGES.UNSUBSCRIBED);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unsubscribe';
      setError(errorMessage);
      console.error('Unsubscribe failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [permissionState.isSubscribed, updatePermissionState]);

  /**
   * Handle service worker messages
   */
  const handleServiceWorkerMessage = useCallback((event: MessageEvent<ServiceWorkerMessage>) => {
    console.log('Service worker message received in context:', event.data);
    
    switch (event.data.type) {
      case 'SUBSCRIPTION_UPDATE':
        updatePermissionState();
        break;
      default:
        // Other message types are handled by the service
        break;
    }
  }, [updatePermissionState]);

  /**
   * Initialize notification service
   */
  const initializeService = async () => {
    console.log('[NotificationProvider] Starting service initialization...');

    if (!notificationService.isSupported()) {
      console.log('[NotificationProvider] Notifications not supported');
      setPermissionState(prev => ({ ...prev, isSupported: false }));
      return;
    }

    console.log('[NotificationProvider] Notifications supported, initializing...');
    setIsLoading(true);
    setError(null);

    try {
      await notificationService.initialize();
      console.log('[NotificationProvider] Service initialized, updating permission state...');
      await updatePermissionState();

      // Set up service worker message listener
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
        console.log('[NotificationProvider] Service worker message listener added');
      }

      console.log('[NotificationProvider] Notification service initialization complete');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize notifications';
      setError(errorMessage);
      console.error('[NotificationProvider] Failed to initialize notification service:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Auto-request permission if configured
   */
  const autoRequestPermission = async () => {
    console.log('[NotificationProvider] Checking if should auto-request permission...');
    console.log('[NotificationProvider] Permission state:', permissionState);

    if (notificationService.shouldAutoRequestPermission()) {
      console.log('[NotificationProvider] Auto-requesting notification permission in 2 seconds...');

      // Add a small delay to avoid interrupting page load
      setTimeout(async () => {
        try {
          console.log('[NotificationProvider] Executing auto permission request...');
          const granted = await requestPermission();
          if (granted) {
            console.log('[NotificationProvider] Permission granted, auto-subscribing...');
            // Auto-subscribe if permission granted
            await subscribe();
          }
        } catch (error) {
          console.log('[NotificationProvider] Auto permission request failed:', error);
          // Don't show error for auto-request failures
        }
      }, 2000);
    } else {
      console.log('[NotificationProvider] Auto-request not needed or disabled');
    }
  };

  /**
   * Initialize on mount
   */
  useEffect(() => {
    console.log('[NotificationProvider] useEffect for initialization triggered');
    initializeService();
  }, []); // Remove dependency to prevent re-initialization

  /**
   * Auto-request permission after initialization
   */
  useEffect(() => {
    console.log('[NotificationProvider] useEffect for auto-request triggered', {
      isSupported: permissionState.isSupported,
      isLoading,
      error
    });

    if (permissionState.isSupported && !isLoading && !error) {
      autoRequestPermission();
    }
  }, [permissionState.isSupported, isLoading, error]); // Remove autoRequestPermission dependency

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      }
    };
  }, [handleServiceWorkerMessage]);

  const contextValue: NotificationContextType = {
    permissionState,
    requestPermission,
    subscribe,
    unsubscribe,
    isLoading,
    error,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

/**
 * Hook to use notification context
 */
export function useNotifications(): NotificationContextType {
  const context = useContext(NotificationContext);
  
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  
  return context;
}
