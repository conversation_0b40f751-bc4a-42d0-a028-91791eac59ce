'use client';

/**
 * Simple test component to isolate notification issues
 */

import React, { useEffect } from 'react';

export function SimpleTest() {
  useEffect(() => {
    console.log('[SimpleTest] Component mounted');
    
    // Test basic browser support
    console.log('[SimpleTest] Service Worker supported:', 'serviceWorker' in navigator);
    console.log('[SimpleTest] Push Manager supported:', 'PushManager' in window);
    console.log('[SimpleTest] Notification supported:', 'Notification' in window);
    console.log('[SimpleTest] Current permission:', 'Notification' in window ? Notification.permission : 'N/A');
    
    // Test service worker registration
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          console.log('[SimpleTest] Service worker registered:', registration);
        })
        .catch(error => {
          console.error('[SimpleTest] Service worker registration failed:', error);
        });
    }
    
    // Test backend connectivity
    fetch('https://gamyday-notification.onrender.com/health')
      .then(response => response.json())
      .then(data => {
        console.log('[SimpleTest] Backend health check:', data);
      })
      .catch(error => {
        console.error('[SimpleTest] Backend health check failed:', error);
      });
      
    // Test VAPID key
    fetch('https://gamyday-notification.onrender.com/api/vapid-public-key')
      .then(response => response.json())
      .then(data => {
        console.log('[SimpleTest] VAPID key:', data);
      })
      .catch(error => {
        console.error('[SimpleTest] VAPID key fetch failed:', error);
      });
  }, []);

  const requestPermission = async () => {
    console.log('[SimpleTest] Requesting permission...');
    
    if (!('Notification' in window)) {
      console.error('[SimpleTest] Notifications not supported');
      return;
    }
    
    if (Notification.permission === 'granted') {
      console.log('[SimpleTest] Permission already granted');
      return;
    }
    
    try {
      const permission = await Notification.requestPermission();
      console.log('[SimpleTest] Permission result:', permission);
    } catch (error) {
      console.error('[SimpleTest] Permission request failed:', error);
    }
  };

  return (
    <div className="bg-red-100 border border-red-400 p-4 rounded-lg mt-4">
      <h3 className="text-lg font-bold text-red-800 mb-2">🔧 Simple Notification Test</h3>
      <p className="text-sm text-red-700 mb-3">
        This is a simplified test component. Check the browser console for logs.
      </p>
      
      <button
        onClick={requestPermission}
        className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
      >
        Request Permission
      </button>
      
      <div className="mt-3 text-xs text-red-600">
        <p>• Check browser console for detailed logs</p>
        <p>• This component tests basic functionality without the complex provider</p>
      </div>
    </div>
  );
}
