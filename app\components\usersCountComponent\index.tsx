import React, { useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { fetchAndSetUsersCount } from "@/app/utils/helper";
import { usePlayerCountRefresh } from "@/app/hooks/usePlayerCountRefresh";
import { PlayerCountService } from "@/app/services/playerCountService";
import { setTotalUsersCount } from "@/redux/slices/totalUsersCountSlice";
import AnnouncementBell from "@/app/components/announcementBell";

const UsersCountComponent = () => {
  const dispatch = useDispatch<AppDispatch>();
  const usersCount = useSelector(
    (state: RootState) => state.totalUsersCount.count
  );

  // Function to refresh user count
  const refreshUsersCount = useCallback(async () => {
    try {
      const count = await PlayerCountService.getTotalUsersCount();
      dispatch(setTotalUsersCount(count));
    } catch (error) {
      console.error("Failed to refresh users count:", error);
      // Fallback to the original method if the new service fails
      try {
        const fetchUsersCount = fetchAndSetUsersCount(dispatch);
        await fetchUsersCount();
      } catch (fallbackError) {
        console.error("Fallback users count fetch also failed:", fallbackError);
      }
    }
  }, [dispatch]);

  // Set up auto-refresh with 10-second interval
  usePlayerCountRefresh(refreshUsersCount, {
    interval: 10000, // 10 seconds
    enabled: true,
    refreshOnMount: false, // We'll do initial fetch in useEffect
    pauseOnHidden: true, // Pause when tab is not visible
  });

  useEffect(() => {
    // Initial fetch on component mount
    const fetchUsersCount = fetchAndSetUsersCount(dispatch);
    fetchUsersCount();
  }, [dispatch]);

  return (
    <div className="flex items-center gap-2 ml-auto pr-3 pl-10">
      <span className="blinking-dot"></span>
      <span className="text-white font-bold text-lg">PLAYERS : {usersCount}</span>
      <div className="ml-3">
        <AnnouncementBell />
      </div>
    </div>
  );
};

export default UsersCountComponent;
