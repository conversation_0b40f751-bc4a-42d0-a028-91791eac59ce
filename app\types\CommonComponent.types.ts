import { User } from "./User";

export interface Tournament {
  tournament_id: string;
  name: string;
  description: string;
  image: string | null;
  prize_pool: number;
  date: string;
}
export interface TournamentCategory {
  type: string;
  tournaments: Tournament[];
}

export interface TimeSlot {
  id: number;
  time: string;
  formatted_time: string;
  bookings_count: number;
  max_players: number;
  is_expired: boolean;
  is_cancelled: boolean;
  cancel_reason: string | null;
}

export interface Payment {
  join_price: string;
  tax_amount: number;
  platform_fees: number;
  final_amount: number;
}

export interface TournamentDetails {
  tournament_id: string;
  date: string;
  prize_pool: number;
  join_price: number;
  bookings_count: number;
  max_players: number;
  map: string;
  mode: string;
  time_slots: TimeSlot[];
  payment: Payment;
  name: string;
  description: string;
  image: string;
  first_prize?: number;
  second_prize?: number;
  third_prize?: number;
  per_kill_prize?: number;
  created_by?: {
    name: string;
    youtube_link: string;
    image: string;
  };
}

export interface GameInfoCardProps {
  tournamentDetails: TournamentDetails | null;
  onJoinNow?: () => void;
  showJoinButton?: boolean;
  isLoading?: boolean;
  selectedTimeSlot?: TimeSlot | null;
}

export interface ModalProps {
  children: React.ReactNode;
  modalOpen: boolean;
  handleModalOpen: (flag: boolean) => void;
  showRefreshButton?: boolean;
  onRefresh?: () => void;
}

export interface SelectScheduleCardProps {
  tournamentDetails: TournamentDetails | null;
  selectedTime: TimeSlot | null;
  setSelectedTime: (timeSlot: TimeSlot) => void;
  onNext: () => void;
  onBack: () => void;
  tournamentBookingsInfo: any | null;
  refreshBookingsData?: () => Promise<any[]>;
}

export interface TournamentConfirmationCardProps {
  tournamentDetails: TournamentDetails | null;
  selectedTime: TimeSlot;
  onJoin: () => void;
  onBack: () => void;
}

export interface GameCardProps {
  tournamentInfo: Tournament;
  isOpen: boolean;
}

export interface BookingData {
  booking_id: string;
  tournament: TournamentDetails;
  time_slot: TimeSlot;
  payment: PaymentDetails;
  created_at: string;
}

export interface PaymentDetails {
  amount: string;
  payment_method: string;
  created_at: string;
}

export interface BookingSuccessProps {
  data: BookingData;
}

export interface MyBookingInfo {
  booking_id: string;
  tournament_name: string;
  tournament_date: string;
  time_slot: string;
  in_game_name: string;
  game_link: string | null;
  room_id: string | null;
  room_password: string | null;
  result: string;
  amount: string;
  status: string;
  created_at: string;
  // booking_amount: string;
}

export interface MybookingsRes {
  count: number;
  next: string | null;
  previous: string | null;
  results: MyBookingInfo[];
}

export interface BookingCardProps {
  data: MyBookingInfo;
}

export interface BookingDetails {
  booking_id: string;
  in_game_name: string;
  tournament: TournamentDetails;
  time_slot: TimeSlot;
  payment: PaymentDetails;
  created_at: string;
  result: string | null;
  game_link: string | null;
  room_id: string | null;
  room_password: string | null;
  status: string;
  amount: string;
}

export interface TournamentSectionProps {
  tournaments: Tournament[];
  sectionTitle: string;
  selectedTournamentId: string | null;
  selectedDateFilter?: string | null;
}

export interface GroupedGameCardProps {
  gameName: string;
  gameImage: string;
  type: string;
  tournamentCount: number;
}

export interface UpdateInGameNameResponse {
  game_link: string | null;
  in_game_name: string;
  room_id: string | null;
  room_password: string | null;
}
export interface Banner {
  image: string;
  link: string;
}

export interface SliderBannerProps {
  banners: Banner[];
}

// Redeem
export interface RedeemData {
  id: number;
  upi_id: string;
  pan_number: string;
  amount: string;
  tax_amount: string;
  credit_amount: string;
  status: "pending" | "completed";
  created_at: string;
}

export interface RedeemRes {
  count: number;
  next: string | null;
  previous: string | null;
  results: RedeemData[];
}

export interface WithdrawalDetailsFormProps {
  handleModal: (isOpen: boolean) => void;
  upi_id: string;
  pan_number: string;
  onUpdateSuccess: () => void;
  openConfirmModal: boolean;
}

export interface RaiseRequestData {
  id: number;
  status: "Pending" | "Completed";
  created_at: string;
  issue: string;
  comments: string;
  booking_id: string | null;
  response: string | null;
  refund_amount: number | null;
}

export interface RaiseRequestRes {
  count: number;
  next: string | null;
  previous: string | null;
  results: RaiseRequestData[];
}

export interface Option {
  id: number;
  query: string;
}

export interface RaiseRequestFormProps {
  options: Option[];
  handleModal: (isOpen: boolean) => void;
  prefillData?: {
    issue: string;
    booking_id: string;
  };
  fetchData?: () => void;
}

export interface ViewRaiseRequestProps {
  requestData: RaiseRequestData | null;
}

export interface LeaderboardData {
  user_id: string;
  name: string;
  total_winnings: number;
  rank: number;
  instagram_link: string | null;
  youtube_link: string | null;
}

export interface LeaderboardRes {
  count: number;
  next: string | null;
  previous: string | null;
  results: LeaderboardData[];
}

export interface UserLeaderboardStatus {
  user_id: string;
  name: string;
  total_winnings: number;
  rank: number;
  instagram_link: string | null;
  youtube_link: string | null;
}

export interface UpdateProfileFormProps {
  handleModal: (isOpen: boolean) => void;
  user: User;
}

export interface StartGameModalProps {
  game_link: string | null;
  room_id: string | null;
  room_password: string | null;
  bookingId?: string;
  onSuccess?: () => void;
  tournamentName?: string;
  inGameName?: string;
  showUpdateInGameNameForm?: boolean;
  createdBy?: {
    name: string;
    youtube_link: string;
    image: string;
  };
}

export interface SocialIcon {
  image: string;
  link: string;
}

export interface PaymentOrderResponse {
  payment_id: string;
  payment_url: string;
}

export interface Transaction {
  transaction_id: string;
  amount: string;
  transaction_direction: "debit" | "credit";
  description: string;
  created_at: string;
}

export interface TransactionsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Transaction[];
}

export interface YoutubePartner {
  username: string;
  youtube_link: string;
  image: string;
}

export interface YoutubePartnerResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: YoutubePartner[];
}

// Games API Types
export interface Game {
  game_id: string;  // API uses game_id
  name: string;     // API uses name
  description?: string; // API uses description
  image?: string;
  category?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  active_player_count?: number;
  
  // Support for your field names as well
  id?: string;
  game_name?: string;
  game_desc?: string;
}

export interface GameDetails extends Game {
  rules?: string[];
  min_players?: number;
  max_players?: number;
  duration?: number;
  prize_pool?: number;
}

export interface Lobby {
  lobby_id: string;
  game_id: string;
  name: string;
  description?: string;
  max_players: number;
  current_players: number;
  active_player_count?: number;
  entry_fee: number;
  prize_pool: number;
  status: 'waiting' | 'active' | 'completed' | 'cancelled';
  start_time: string;
  end_time?: string;
  is_joinable: boolean;
  created_at: string;
}

export interface GameBooking {
  booking_id: string;
  game_id: string;
  lobby_id: string;
  game_name: string;
  user_id: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  entry_fee: number;
  booking_time: string;
  game_start_time: string;
  result?: GameResult;
  return_url?: string;
}

export interface GameResult {
  result_id: string;
  booking_id: string;
  position: number;
  score?: number;
  prize_amount: number;
  status: 'win' | 'lose' | 'draw';
  completed_at: string;
}

export interface CreateGameBookingRequest {
  game_id: string;
  lobby_id: string;
  game_name: string;
  return_to: string;
  amount?: number;
  username?: string;
}

export interface GamesResponse {
  data: Game[];
  count: number;
  next?: string;
  previous?: string;
}

export interface GameBookingsResponse {
  data: GameBooking[];
  count: number;
  next?: string;
  previous?: string;
}
