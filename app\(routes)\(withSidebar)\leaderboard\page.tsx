"use client";
import { useEffect, useState } from "react";
import { LeaderboardRes, UserLeaderboardStatus } from "@/app/types/CommonComponent.types";
import Loader from "@/app/components/common/Loader";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import ServerError from "@/app/components/common/ServerError";
import Pagination from "@/app/components/pagination";

const tableHeadings = [
  { id: 1, label: "Rank" },
  { id: 2, label: "Name" },
  { id: 3, label: "Total Winnings" },
  { id: 4, label: "Instagram" },
  { id: 5, label: "YouTube" },
];

// Function to get rank badge based on leaderboard position
const getRankBadge = (rank: number): string => {
  if (rank >= 1 && rank <= 20) return "/ranks/diamond.png";
  if (rank >= 21 && rank <= 50) return "/ranks/platinum.png";
  if (rank >= 51 && rank <= 100) return "/ranks/gold.png";
  if (rank >= 101 && rank <= 500) return "/ranks/silver.png";
  if (rank >= 501 && rank <= 1000) return "/ranks/bronze.png";
  if (rank >= 1001 && rank <= 5000) return "/ranks/iron.png";
  if (rank >= 5001 && rank <= 10000) return "/ranks/wood.png";
  return "/ranks/unranked.png"; // 10001+
};

// Function to get rank name for alt text
const getRankName = (rank: number): string => {
  if (rank >= 1 && rank <= 20) return "Diamond";
  if (rank >= 21 && rank <= 50) return "Platinum";
  if (rank >= 51 && rank <= 100) return "Gold";
  if (rank >= 101 && rank <= 500) return "Silver";
  if (rank >= 501 && rank <= 1000) return "Bronze";
  if (rank >= 1001 && rank <= 5000) return "Iron";
  if (rank >= 5001 && rank <= 10000) return "Wood";
  return "Unranked"; // 10001+
};

// Rank reference data for the reference table
const rankTiers = [
  { name: "Diamond", range: "Ranks 1-20", image: "/ranks/diamond.png" },
  { name: "Platinum", range: "Ranks 21-50", image: "/ranks/platinum.png" },
  { name: "Gold", range: "Ranks 51-100", image: "/ranks/gold.png" },
  { name: "Silver", range: "Ranks 101-500", image: "/ranks/silver.png" },
  { name: "Bronze", range: "Ranks 501-1000", image: "/ranks/bronze.png" },
  { name: "Iron", range: "Ranks 1001-5000", image: "/ranks/iron.png" },
  { name: "Wood", range: "Ranks 5001-10000", image: "/ranks/wood.png" },
  { name: "Unranked", range: "Ranks 10001+", image: "/ranks/unranked.png" },
];

const LeaderboardPage = () => {
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardRes>(
    {} as LeaderboardRes
  );
  const [userLeaderboardStatus, setUserLeaderboardStatus] = useState<UserLeaderboardStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUserStatusLoading, setIsUserStatusLoading] = useState(true);
  const [error, setError] = useState<any>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  const totalPages = Math.ceil(leaderboardData?.count / itemsPerPage);

  const fetchLeaderboard = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.LEADERBOARD(currentPage));
      if (response.status === 200) {
        setLeaderboardData(response?.data);
        setIsLoading(false);
      }
    } catch (error: any) {
      setError(error);
    }
  };

  const fetchUserLeaderboardStatus = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.USER_LEADERBOARD_STATUS);
      if (response.status === 200) {
        setUserLeaderboardStatus(response?.data?.data);
      }
    } catch (error: any) {
      console.error("Error fetching user leaderboard status:", error);
    } finally {
      setIsUserStatusLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaderboard();
    fetchUserLeaderboardStatus();
  }, [currentPage]);

  if (error?.response?.status === 500) {
    return <ServerError />;
  }

  return (
    <div className="p-8 w-full">
      <div className="pt-8 flex flex-row gap-8 justify-center px-4">
        {/* Main leaderboard table */}
        <div>
          {isLoading && <Loader />}

          {!isLoading && leaderboardData?.results?.length === 0 && (
            <p className="text-gray-300 text-lg font-medium">
              No detail available
            </p>
          )}

          {leaderboardData?.results?.length > 0 && (
            <div className="overflow-hidden shadow ring-1 ring-white rounded-lg w-[1000px]">
            <table className="divide-y divide-white w-full ">
              <thead className="bg-red-500 rounded-lg">
                <tr>
                  {tableHeadings.map((heading, index) => (
                    <th
                      key={heading.id}
                      scope="col"
                      className={`px-3 py-3.5 text-left text-xl font-semibold text-white ${
                        index === 0 ? "pl-12" : ""
                      }`}
                    >
                      {heading.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-white">
                {leaderboardData?.results?.map((leader) => (
                  <tr key={leader?.rank}>
                    <td
                      className={`whitespace-nowrap py-4 pl-8 pr-3 text-white ${
                        leader?.rank === 1
                          ? "font-bold text-2xl"
                          : leader?.rank === 2
                          ? "font-semibold text-xl"
                          : leader?.rank === 3
                          ? "font-semibold text-lg"
                          : "text-base font-semibold"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <span className="w-8 text-right">{leader?.rank}.</span>
                      </div>
                    </td>
                    <td
                      className={`whitespace-nowrap px-3 py-4 text-white ${
                        leader?.rank === 1
                          ? "font-bold text-2xl"
                          : leader?.rank === 2
                          ? "font-semibold text-xl"
                          : leader?.rank === 3
                          ? "font-semibold text-lg"
                          : "text-base font-semibold"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className="relative group">
                          <img
                            src={getRankBadge(leader?.rank)}
                            alt={`${getRankName(leader?.rank)} rank`}
                            className="w-9 h-9 object-contain cursor-help"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/ranks/unranked.png';
                            }}
                          />
                          {/* Tooltip */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                            {getRankName(leader?.rank)}
                          </div>
                        </div>
                        <span>{leader?.name}</span>
                      </div>
                    </td>
                    <td
                      className={`whitespace-nowrap px-3 pl-5 py-4 text-white ${
                        leader?.rank === 1
                          ? "font-bold text-2xl"
                          : leader?.rank === 2
                          ? "font-semibold text-xl"
                          : leader?.rank === 3
                          ? "font-semibold text-lg"
                          : "text-base font-semibold"
                      }`}
                    >
                      ₹{leader?.total_winnings.toLocaleString("en-IN")}
                    </td>
                    <td
                      className={`whitespace-nowrap px-3 py-4 text-white ${
                        leader?.rank === 1
                          ? "font-bold text-2xl"
                          : leader?.rank === 2
                          ? "font-semibold text-xl"
                          : leader?.rank === 3
                          ? "font-semibold text-lg"
                          : "text-base font-semibold"
                      }`}
                    >
                      {leader?.instagram_link ? (
                        <a href={leader.instagram_link} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-medium rounded-md hover:from-purple-600 hover:to-pink-600 transition-all duration-200">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                          </svg>
                          Instagram
                        </a>
                      ) : (
                        <span className="text-gray-500 text-sm">-</span>
                      )}
                    </td>
                    <td
                      className={`whitespace-nowrap px-3 py-4 text-white ${
                        leader?.rank === 1
                          ? "font-bold text-2xl"
                          : leader?.rank === 2
                          ? "font-semibold text-xl"
                          : leader?.rank === 3
                          ? "font-semibold text-lg"
                          : "text-base font-semibold"
                      }`}
                    >
                      {leader?.youtube_link ? (
                        <a href={leader.youtube_link} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 transition-all duration-200">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                          </svg>
                          YouTube
                        </a>
                      ) : (
                        <span className="text-gray-500 text-sm">-</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            </div>
          )}
        </div>

        {/* User's leaderboard status box */}
        <div className="w-80">
          {isUserStatusLoading ? (
            <div className="bg-gray-800 rounded-lg p-6 shadow-lg h-64 flex items-center justify-center">
              <Loader />
            </div>
          ) : userLeaderboardStatus ? (
            <div className="bg-gray-800 rounded-lg shadow-lg h-80 overflow-hidden ring-1 ring-white">
              <div className="bg-red-500 rounded-t-lg">
                <h2 className="text-xl font-semibold text-white py-3.5 text-center">Your Ranking</h2>
              </div>
              <div className="px-6 py-3">
                <table className="w-full">
                  <tbody>
                    <tr className="border-b border-white">
                      <td className="text-white -400 py-2">Rank</td>
                      <td className="text-white font-bold text-xl text-right pb-2">
                        <div className="flex items-center justify-end gap-2">
                          <span>{userLeaderboardStatus.rank}</span>
                          <div className="relative group">
                            <img
                              src={getRankBadge(userLeaderboardStatus.rank)}
                              alt={`${getRankName(userLeaderboardStatus.rank)} rank`}
                              className="w-9 h-9 object-contain cursor-help"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = '/ranks/unranked.png';
                              }}
                            />
                            {/* Tooltip */}
                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                              {getRankName(userLeaderboardStatus.rank)}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr className="border-b border-white">
                      <td className="text-white-400 py-2">Name</td>
                      <td className="text-white font-semibold text-right pb-2">{userLeaderboardStatus.name}</td>
                    </tr>
                    <tr className="border-b border-white">
                      <td className="text-white-400 py-2">Winnings</td>
                      <td className="text-white font-bold text-right">₹{userLeaderboardStatus.total_winnings.toLocaleString("en-IN")}</td>
                    </tr>
                    <tr className="border-b border-white">
                      <td className="text-white-400 py-2">Instagram</td>
                      <td className="text-white text-right pb-2">
                        {userLeaderboardStatus.instagram_link ? (
                          <a href={userLeaderboardStatus.instagram_link} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-medium rounded-md hover:from-purple-600 hover:to-pink-600 transition-all duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                            Instagram
                          </a>
                        ) : (
                          <span className="text-gray-500 text-sm">-</span>
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td className="text-white-400 py-2">YouTube</td>
                      <td className="text-white text-right">
                        {userLeaderboardStatus.youtube_link ? (
                          <a href={userLeaderboardStatus.youtube_link} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 transition-all duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                            </svg>
                            YouTube
                          </a>
                        ) : (
                          <span className="text-gray-500 text-sm">-</span>
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="bg-gray-800 rounded-lg p-6 shadow-lg h-64 flex items-center justify-center">
              <p className="text-gray-400 text-center">Login to view your ranking</p>
            </div>
          )}

          {/* Rank Reference Table */}
          <div className="bg-gray-800 rounded-lg shadow-lg mt-4 overflow-hidden ring-1 ring-white">
            <div className="bg-red-500 rounded-t-lg">
              <h2 className="text-lg font-semibold text-white py-3 text-center">Rank Tiers</h2>
            </div>
            <div className="p-4">
              <div className="space-y-2">
                {rankTiers.map((tier) => (
                  <div key={tier.name} className="flex items-center justify-between py-2 border-b border-[#2a2c2e] last:border-b-0">
                    <div className="flex items-center gap-3">
                      <div className="relative group">
                        <img
                          src={tier.image}
                          alt={`${tier.name} rank`}
                          className="w-9 h-9 object-contain cursor-help"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/ranks/unranked.png';
                          }}
                        />
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                          {tier.name}
                        </div>
                      </div>
                      <span className="text-white font-medium text-sm">{tier.name}</span>
                    </div>
                    <span className="text-gray-400 text-sm">{tier.range}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {leaderboardData?.count > 0 && (
        <div className="mt-6 flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={(page) => setCurrentPage(page)}
          />
        </div>
      )}
    </div>
  );
};

export default LeaderboardPage;
