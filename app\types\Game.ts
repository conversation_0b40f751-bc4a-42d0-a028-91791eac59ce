export interface Game {
  id: string;
  game_name: string;
  game_desc: string;
  image?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
  active_player_count?: number;
}

export interface GameResult {
  id: string;
  game_id: string;
  lobby_id: string;
  user_id: string;
  score: number;
  rank: number;
  status: 'win' | 'lose' | 'draw';
  created_at: string;
}

export interface Lobby {
  id: string;
  game_id: string;
  lobby_name: string;
  lobby_desc: string;
  lobby_price: number;
  max_players?: number;
  current_players?: number;
  active_player_count?: number;
  status?: 'open' | 'closed' | 'in_progress' | 'active' | 'full';
  created_at?: string;
  updated_at?: string;
}

export interface GamesSectionProps {
  games: Game[];
  title: string;
}

export interface GameCardProps {
  game: Game;
  onClick?: () => void;
}
