"use client";
import React from "react";
import { formatDate } from "@/app/utils/helper";
import { ChevronDownIcon } from "@heroicons/react/24/outline";

interface DateFilterProps {
  availableDates: string[];
  selectedDate: string | null;
  onDateChange: (date: string | null) => void;
  className?: string;
}

const DateFilter: React.FC<DateFilterProps> = ({
  availableDates,
  selectedDate,
  onDateChange,
  className = "",
}) => {
  const handleDateChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    onDateChange(value === "all" ? null : value);
  };

  // Don't render if no dates are available
  if (!availableDates || availableDates.length === 0) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <select
          value={selectedDate || "all"}
          onChange={handleDateChange}
          className="appearance-none bg-[#141517] text-white px-4 py-2 pr-10 rounded-md font-medium hover:bg-[#c9ff88] hover:text-black transition-all border border-[#2a2c2e] focus:outline-none focus:ring-2 focus:ring-[#c9ff88] focus:border-transparent cursor-pointer min-w-[180px]"
        >
          <option value="all">All Dates</option>
          {availableDates.map((date) => (
            <option key={date} value={date} className="bg-[#141517] text-white">
              {formatDate(date)}
            </option>
          ))}
        </select>
        <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-current pointer-events-none" />
      </div>
    </div>
  );
};

export default DateFilter;
