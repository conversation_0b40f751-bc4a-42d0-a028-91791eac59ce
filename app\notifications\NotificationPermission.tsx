'use client';

/**
 * Notification Permission Component
 * Handles the UI for requesting notification permissions and managing subscription state
 */

import React, { useState, useEffect } from 'react';
import { useNotifications } from './NotificationProvider';
import { PERMISSION_CONFIG } from './config';

interface NotificationPermissionProps {
  className?: string;
  showOnlyWhenNeeded?: boolean;
  autoHide?: boolean;
  hideDelay?: number;
}

export function NotificationPermission({
  className = '',
  showOnlyWhenNeeded = true,
  autoHide = true,
  hideDelay = 5000,
}: NotificationPermissionProps) {
  const {
    permissionState,
    requestPermission,
    subscribe,
    unsubscribe,
    isLoading,
    error,
  } = useNotifications();

  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const [showError, setShowError] = useState(false);

  /**
   * Determine if component should be visible
   */
  useEffect(() => {
    if (!PERMISSION_CONFIG.showUI) {
      setIsVisible(false);
      return;
    }

    if (isDismissed) {
      setIsVisible(false);
      return;
    }

    if (!permissionState.isSupported) {
      setIsVisible(false);
      return;
    }

    if (showOnlyWhenNeeded) {
      // Show only when permission is default (not granted or denied)
      // and user is not subscribed
      const shouldShow = 
        permissionState.permission === 'default' && 
        !permissionState.isSubscribed;
      
      setIsVisible(shouldShow);
    } else {
      // Always show when supported
      setIsVisible(true);
    }
  }, [
    permissionState.isSupported,
    permissionState.permission,
    permissionState.isSubscribed,
    showOnlyWhenNeeded,
    isDismissed,
  ]);

  /**
   * Auto-hide after successful subscription
   */
  useEffect(() => {
    if (autoHide && permissionState.isSubscribed && isVisible) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, hideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, permissionState.isSubscribed, isVisible, hideDelay]);

  /**
   * Show error temporarily
   */
  useEffect(() => {
    if (error) {
      setShowError(true);
      const timer = setTimeout(() => {
        setShowError(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [error]);

  /**
   * Handle enable notifications click
   */
  const handleEnableNotifications = async () => {
    try {
      const granted = await requestPermission();
      if (granted) {
        await subscribe();
      }
    } catch (error) {
      console.error('Failed to enable notifications:', error);
    }
  };

  /**
   * Handle disable notifications click
   */
  const handleDisableNotifications = async () => {
    try {
      await unsubscribe();
    } catch (error) {
      console.error('Failed to disable notifications:', error);
    }
  };

  /**
   * Handle dismiss
   */
  const handleDismiss = () => {
    setIsDismissed(true);
    setIsVisible(false);
  };

  /**
   * Get status message
   */
  const getStatusMessage = () => {
    if (error && showError) {
      return {
        type: 'error' as const,
        message: error,
      };
    }

    if (permissionState.isSubscribed) {
      return {
        type: 'success' as const,
        message: 'Notifications enabled! You\'ll receive updates about your games.',
      };
    }

    if (permissionState.permission === 'denied') {
      return {
        type: 'warning' as const,
        message: 'Notifications are blocked. Enable them in your browser settings to receive updates.',
      };
    }

    return {
      type: 'info' as const,
      message: 'Enable notifications to get updates about your games and tournaments.',
    };
  };

  if (!isVisible) {
    return null;
  }

  const status = getStatusMessage();

  return (
    <div className={`notification-permission ${className}`}>
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-lg shadow-lg">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            {/* Icon */}
            <div className="flex-shrink-0 mt-1">
              {status.type === 'success' && (
                <svg className="w-5 h-5 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
              {status.type === 'error' && (
                <svg className="w-5 h-5 text-red-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              )}
              {status.type === 'warning' && (
                <svg className="w-5 h-5 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              )}
              {status.type === 'info' && (
                <svg className="w-5 h-5 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              )}
            </div>

            {/* Content */}
            <div className="flex-1">
              <h3 className="text-sm font-medium">
                {permissionState.isSubscribed ? 'Notifications Enabled' : 'Enable Notifications'}
              </h3>
              <p className="text-sm text-blue-100 mt-1">
                {status.message}
              </p>

              {/* Action buttons */}
              <div className="mt-3 flex space-x-2">
                {!permissionState.isSubscribed && permissionState.permission !== 'denied' && (
                  <button
                    onClick={handleEnableNotifications}
                    disabled={isLoading}
                    className="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isLoading ? 'Enabling...' : 'Enable'}
                  </button>
                )}

                {permissionState.isSubscribed && (
                  <button
                    onClick={handleDisableNotifications}
                    disabled={isLoading}
                    className="bg-white/20 text-white px-3 py-1 rounded text-sm font-medium hover:bg-white/30 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isLoading ? 'Disabling...' : 'Disable'}
                  </button>
                )}

                <button
                  onClick={handleDismiss}
                  className="text-blue-100 hover:text-white text-sm font-medium transition-colors"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>

          {/* Close button */}
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 ml-4 text-blue-200 hover:text-white transition-colors"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
