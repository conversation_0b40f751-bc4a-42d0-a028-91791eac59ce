import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import api from "@/app/utils/axiosInstance";
import { useEffect } from "react";
import Link from "next/link";
import { useState } from "react";
import Loader from "../common/Loader";
import { ConfirmModalContentProps } from "../confirmRedeem";

const ConfirmWalletTopup: React.FC<ConfirmModalContentProps> = ({
  onConfirm,
  onClose,
  isRedeemConfirming,
  redeem_wallet,
  hideWarningMessage,
}) => {
  const [redeemBreakupData, setRedeemBreakupData] = useState<any>();
  const [isLoading, setIsLoading] = useState(true);

  const fetchRedeemBreakup = async () => {
    setIsLoading(true);
    try {
      const response = await api.post(API_ENDPOINTS.REDEEM_DATA, {
        amount: redeem_wallet,
      });
      if (response.status === 200) {
        setRedeemBreakupData(response?.data?.data);
      }
    } catch (error: any) {
      console.log(error);
      //   setApiError(error?.response?.data?.message);
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    fetchRedeemBreakup();
  }, []);
  
  return (
    <>
      {isLoading ? (
        <div className="min-w-[520px] min-h-[280px] flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl">
          <Loader />
        </div>
      ) : (
        <div className="px-8 py-6 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-2xl border border-gray-700">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-white mb-2">
              Confirm Wallet Top-up
            </h3>
            <p className="text-gray-300 text-sm">Transfer funds from Red Wallet to Green Wallet</p>
          </div>

          <div className="bg-gray-700/50 rounded-lg p-6 mb-6 border border-gray-600">
            <div className="flex items-center justify-between py-4">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="font-semibold text-2xl text-white">
                  Transfer Amount (Red → Green)
                </span>
              </div>
              <div className="text-right">
                <span className="font-bold text-2xl text-green-400">
                  ₹{redeemBreakupData?.redeem_amount?.toLocaleString("en-IN")}
                </span>
              </div>
            </div>

            {/* <div className="flex items-center justify-between py-3 border-t border-gray-600">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <span className="font-semibold text-lg text-gray-300">
                  Applicable Tax / Platform Levy {"\u2013"} 30%
                </span>
              </div>
              <div className="text-right">
                <span className="font-medium text-lg text-orange-400">
                  -₹{redeemBreakupData?.tax_amount?.toLocaleString("en-IN")}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between py-4 border-t border-gray-600 bg-green-500/10 rounded-lg px-4 mt-4">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="font-bold text-xl text-white">
                  Final Green Wallet Credit
                </span>
              </div>
              <div className="text-right">
                <span className="font-bold text-xl text-green-400">
                  ₹{redeemBreakupData?.final_amount?.toLocaleString("en-IN")}
                </span>
              </div>
            </div> */}
          </div>
          <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-sm text-blue-200 leading-relaxed">
                  Please read our{" "}
                  <Link
                    href="/tax-policy"
                    className="text-blue-400 hover:text-blue-300 underline font-medium transition-colors"
                  >
                    Tax &amp; Fee Policy
                  </Link>{" "}
                  for complete details about platform charges and regulations.
                </p>
                {!hideWarningMessage && (
                  <div className="mt-3 p-3 bg-red-500/10 border border-red-500/30 rounded-md">
                    <p className="text-sm text-red-300 leading-relaxed">
                      <span className="font-semibold block mb-1">
                        ⏱️ Processing Time: Credits will be available in 2–5 working days
                      </span>
                      <span className="font-semibold">
                        📅 Daily Limit: Players can redeem once per day, including Top-ups using the Red Wallet
                      </span>
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex gap-4 pt-4 border-t border-gray-600">
            <button
              onClick={onClose}
              className="flex-1 px-6 py-3 bg-gray-600 hover:bg-gray-500 text-white rounded-lg font-semibold transition-all duration-200 border border-gray-500 hover:border-gray-400"
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              disabled={isRedeemConfirming}
              className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed disabled:hover:from-green-600 disabled:hover:to-green-500"
            >
              {isRedeemConfirming ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Confirm Top-up
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ConfirmWalletTopup;
