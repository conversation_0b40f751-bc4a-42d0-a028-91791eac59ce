'use client';

/**
 * Notification Test Component
 * A simple component to test notification functionality
 * This can be temporarily added to any page for testing
 */

import React from 'react';
import { useNotifications } from './NotificationProvider';

export function NotificationTest() {
  const {
    permissionState,
    requestPermission,
    subscribe,
    unsubscribe,
    isLoading,
    error,
  } = useNotifications();

  const handleRequestPermission = async () => {
    try {
      const granted = await requestPermission();
      console.log('Permission granted:', granted);
    } catch (error) {
      console.error('Permission request failed:', error);
    }
  };

  const handleSubscribe = async () => {
    try {
      const success = await subscribe();
      console.log('Subscription success:', success);
    } catch (error) {
      console.error('Subscription failed:', error);
    }
  };

  const handleUnsubscribe = async () => {
    try {
      const success = await unsubscribe();
      console.log('Unsubscribe success:', success);
    } catch (error) {
      console.error('Unsubscribe failed:', error);
    }
  };

  const sendTestNotification = async () => {
    try {
      const response = await fetch('https://gamyday-notification.onrender.com/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payload: {
            title: 'Test Notification',
            body: 'This is a test notification from GamyDay!',
            icon: '/favicon.png',
            badge: '/favicon.png',
            url: '/',
            data: {
              test: true,
              timestamp: new Date().toISOString(),
            },
          },
          target_type: 'all',
        }),
      });

      const result = await response.json();
      console.log('Test notification sent:', result);
    } catch (error) {
      console.error('Failed to send test notification:', error);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto mt-8">
      <h2 className="text-xl font-bold mb-4 text-gray-800">Notification Test</h2>
      
      <div className="space-y-4">
        {/* Status Display */}
        <div className="bg-gray-100 p-3 rounded">
          <h3 className="font-semibold text-gray-700 mb-2">Status</h3>
          <div className="text-sm space-y-1">
            <p><strong>Supported:</strong> {permissionState.isSupported ? 'Yes' : 'No'}</p>
            <p><strong>Permission:</strong> {permissionState.permission}</p>
            <p><strong>Subscribed:</strong> {permissionState.isSubscribed ? 'Yes' : 'No'}</p>
            <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
            {error && <p className="text-red-600"><strong>Error:</strong> {error}</p>}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <button
            onClick={handleRequestPermission}
            disabled={isLoading || permissionState.permission === 'granted'}
            className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Request Permission
          </button>

          <button
            onClick={handleSubscribe}
            disabled={isLoading || !permissionState.isSupported || permissionState.permission !== 'granted' || permissionState.isSubscribed}
            className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Subscribe
          </button>

          <button
            onClick={handleUnsubscribe}
            disabled={isLoading || !permissionState.isSubscribed}
            className="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Unsubscribe
          </button>

          <button
            onClick={sendTestNotification}
            disabled={!permissionState.isSubscribed}
            className="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Send Test Notification
          </button>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 p-3 rounded text-sm text-blue-800">
          <h4 className="font-semibold mb-1">Instructions:</h4>
          <ol className="list-decimal list-inside space-y-1">
            <li>Click "Request Permission" to ask for notification permission</li>
            <li>Click "Subscribe" to subscribe to push notifications</li>
            <li>Click "Send Test Notification" to test the system</li>
            <li>Check browser console for detailed logs</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
