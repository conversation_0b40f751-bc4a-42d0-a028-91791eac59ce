"use client";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { Game } from "@/app/types/Game";

/**
 * Service for fetching updated player count data
 */
export class PlayerCountService {
  /**
   * Fetch the total users count across all games
   * @returns Promise<number> - The total number of active users
   */
  static async getTotalUsersCount(): Promise<number> {
    try {
      const response = await api.get("/users-count");
      return response.data.count || 0;
    } catch (error) {
      console.error("Error fetching total users count:", error);
      throw error;
    }
  }

  /**
   * Fetch all games with updated player counts
   * @returns Promise<Game[]> - Array of games with current player counts
   */
  static async getAllGamesWithPlayerCounts(): Promise<Game[]> {
    try {
      const response = await api.get<Game[]>(API_ENDPOINTS.GET_ALL_GAMES);
      return response.data || [];
    } catch (error) {
      console.error("Error fetching games with player counts:", error);
      throw error;
    }
  }

  /**
   * Fetch lobbies for a specific game with updated player counts
   * @param gameId - The ID of the game
   * @returns Promise<any[]> - Array of lobbies with current player counts
   */
  static async getGameLobbiesWithPlayerCounts(gameId: string): Promise<any[]> {
    try {
      const response = await api.get(API_ENDPOINTS.GET_GAME_LOBBIES(gameId));
      const apiLobbies = response.data || [];
      
      // Format lobbies to ensure consistent structure
      return apiLobbies.map((lobby: any) => ({
        ...lobby,
        status: lobby.status || "open",
        max_players: lobby.max_players || 2,
        current_players: lobby.current_players || 1,
        active_player_count: lobby.active_player_count || 0
      }));
    } catch (error) {
      console.error(`Error fetching lobbies for game ${gameId}:`, error);
      throw error;
    }
  }

  /**
   * Fetch player count for a specific game
   * @param gameId - The ID of the game
   * @returns Promise<number> - The active player count for the game
   */
  static async getGamePlayerCount(gameId: string): Promise<number> {
    try {
      const response = await api.get(API_ENDPOINTS.GET_GAME_DETAILS(gameId));
      return response.data.active_player_count || 0;
    } catch (error) {
      console.error(`Error fetching player count for game ${gameId}:`, error);
      throw error;
    }
  }

  /**
   * Fetch player count for a specific lobby
   * @param lobbyId - The ID of the lobby
   * @returns Promise<number> - The active player count for the lobby
   */
  static async getLobbyPlayerCount(lobbyId: string): Promise<number> {
    try {
      const response = await api.get(API_ENDPOINTS.GET_LOBBY_DETAILS(lobbyId));
      return response.data.active_player_count || 0;
    } catch (error) {
      console.error(`Error fetching player count for lobby ${lobbyId}:`, error);
      throw error;
    }
  }

  /**
   * Batch fetch player counts for multiple games
   * @param gameIds - Array of game IDs
   * @returns Promise<Record<string, number>> - Object mapping game IDs to player counts
   */
  static async getBatchGamePlayerCounts(gameIds: string[]): Promise<Record<string, number>> {
    try {
      const promises = gameIds.map(async (gameId) => {
        try {
          const count = await this.getGamePlayerCount(gameId);
          return { gameId, count };
        } catch (error) {
          console.error(`Failed to fetch count for game ${gameId}:`, error);
          return { gameId, count: 0 };
        }
      });

      const results = await Promise.all(promises);
      return results.reduce((acc, { gameId, count }) => {
        acc[gameId] = count;
        return acc;
      }, {} as Record<string, number>);
    } catch (error) {
      console.error("Error in batch fetching game player counts:", error);
      throw error;
    }
  }

  /**
   * Batch fetch player counts for multiple lobbies
   * @param lobbyIds - Array of lobby IDs
   * @returns Promise<Record<string, number>> - Object mapping lobby IDs to player counts
   */
  static async getBatchLobbyPlayerCounts(lobbyIds: string[]): Promise<Record<string, number>> {
    try {
      const promises = lobbyIds.map(async (lobbyId) => {
        try {
          const count = await this.getLobbyPlayerCount(lobbyId);
          return { lobbyId, count };
        } catch (error) {
          console.error(`Failed to fetch count for lobby ${lobbyId}:`, error);
          return { lobbyId, count: 0 };
        }
      });

      const results = await Promise.all(promises);
      return results.reduce((acc, { lobbyId, count }) => {
        acc[lobbyId] = count;
        return acc;
      }, {} as Record<string, number>);
    } catch (error) {
      console.error("Error in batch fetching lobby player counts:", error);
      throw error;
    }
  }
}
