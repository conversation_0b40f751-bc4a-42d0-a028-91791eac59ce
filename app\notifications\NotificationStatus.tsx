'use client';

/**
 * Notification Status Component
 * Shows current notification status and provides manual controls
 */

import React from 'react';
import { useNotifications } from './NotificationProvider';

export function NotificationStatus() {
  const {
    permissionState,
    requestPermission,
    subscribe,
    unsubscribe,
    isLoading,
    error,
  } = useNotifications();

  const sendTestNotification = async () => {
    try {
      const response = await fetch('https://gamyday-notification.onrender.com/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payload: {
            title: 'GamyDay Test Notification',
            body: 'This is a test notification! Your notification system is working perfectly! 🎉',
            icon: '/favicon.png',
            badge: '/favicon.png',
            url: '/',
            data: {
              test: true,
              timestamp: new Date().toISOString(),
            },
          },
          target_type: 'all',
        }),
      });

      const result = await response.json();
      console.log('Test notification sent:', result);
      
      if (result.success) {
        alert('Test notification sent successfully! Check your browser for the notification.');
      } else {
        alert('Failed to send test notification: ' + (result.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Failed to send test notification:', error);
      alert('Failed to send test notification: ' + error.message);
    }
  };

  const getStatusColor = () => {
    if (!permissionState.isSupported) return 'bg-gray-500';
    if (permissionState.permission === 'denied') return 'bg-red-500';
    if (permissionState.permission === 'granted' && permissionState.isSubscribed) return 'bg-green-500';
    if (permissionState.permission === 'granted') return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const getStatusText = () => {
    if (!permissionState.isSupported) return 'Not Supported';
    if (permissionState.permission === 'denied') return 'Permission Denied';
    if (permissionState.permission === 'granted' && permissionState.isSubscribed) return 'Active & Subscribed';
    if (permissionState.permission === 'granted') return 'Permission Granted';
    return 'Permission Needed';
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">🔔 Notification Status</h3>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()}`}></div>
          <span className="text-sm font-medium text-gray-600">{getStatusText()}</span>
        </div>
      </div>

      {/* Status Details */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div>
          <span className="font-medium text-gray-600">Supported:</span>
          <span className="ml-2">{permissionState.isSupported ? '✅ Yes' : '❌ No'}</span>
        </div>
        <div>
          <span className="font-medium text-gray-600">Permission:</span>
          <span className="ml-2 capitalize">{permissionState.permission}</span>
        </div>
        <div>
          <span className="font-medium text-gray-600">Subscribed:</span>
          <span className="ml-2">{permissionState.isSubscribed ? '✅ Yes' : '❌ No'}</span>
        </div>
        <div>
          <span className="font-medium text-gray-600">Loading:</span>
          <span className="ml-2">{isLoading ? '⏳ Yes' : '✅ No'}</span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
          <p className="text-red-700 text-sm">
            <strong>Error:</strong> {error}
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        {permissionState.permission !== 'granted' && (
          <button
            onClick={requestPermission}
            disabled={isLoading || !permissionState.isSupported}
            className="bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Requesting...' : 'Request Permission'}
          </button>
        )}

        {permissionState.permission === 'granted' && !permissionState.isSubscribed && (
          <button
            onClick={subscribe}
            disabled={isLoading}
            className="bg-green-500 text-white px-3 py-2 rounded text-sm hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Subscribing...' : 'Subscribe'}
          </button>
        )}

        {permissionState.isSubscribed && (
          <>
            <button
              onClick={sendTestNotification}
              className="bg-purple-500 text-white px-3 py-2 rounded text-sm hover:bg-purple-600"
            >
              Send Test Notification
            </button>
            <button
              onClick={unsubscribe}
              disabled={isLoading}
              className="bg-red-500 text-white px-3 py-2 rounded text-sm hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Unsubscribing...' : 'Unsubscribe'}
            </button>
          </>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-blue-50 rounded text-sm text-blue-800">
        <p className="font-medium mb-1">Instructions:</p>
        <ul className="list-disc list-inside space-y-1">
          <li>The system should automatically subscribe when permission is granted</li>
          <li>Use "Send Test Notification" to verify the system is working</li>
          <li>Check your browser's notification area for incoming notifications</li>
          <li>Check the browser console for detailed logs</li>
        </ul>
      </div>
    </div>
  );
}
