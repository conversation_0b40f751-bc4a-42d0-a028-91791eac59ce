"use client";
import { useEffect, useState, useCallback } from "react";
import {
  Banner,
  SocialIcon,
  TournamentCategory,
} from "@/app/types/CommonComponent.types";
import { Game } from "@/app/types/Game";
import withSidebar from "./hoc/withSidebar";
import Loader from "./components/common/Loader";
import api from "./utils/axiosInstance";
import { API_ENDPOINTS } from "./constants/apiEndpoints";
import TournamentSection from "./components/tournamentSection";
import GamesSection from "./components/gamesSection";
import SliderBanner from "./components/banner";
import { useSearchParams } from "next/navigation";
import { useSidebar } from "./context/SidebarContext";
import GamyDayPartners from "./components/gamyDayPartners";
import Link from "next/link";
import { usePlayerCountRefresh } from "./hooks/usePlayerCountRefresh";
import { NotificationTest } from "./notifications";
import { PlayerCountService } from "./services/playerCountService";

const Home = () => {
  const { isExpanded } = useSidebar();
  const [tournamentsData, setTournamentsData] = useState<TournamentCategory[]>(
    []
  );
  const [gamesData, setGamesData] = useState<Game[]>([]);

  const [banners, setBanners] = useState<Banner[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTournamentId, setSelectedTournamentId] = useState<
    string | null
  >(null);
  const [socialIcons, setSocialIcons] = useState<SocialIcon[]>([]);
  const searchParams = useSearchParams();

  useEffect(() => {
    const tournamentId = searchParams.get("tournament_id");
    if (tournamentId) {
      setSelectedTournamentId(tournamentId);
    }
  }, [searchParams]);

  const fetchInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch games and banners first - these are the priority
      try {
        const [bannersResponse, gamesResponse] = await Promise.all([
          api.get<Banner[]>(API_ENDPOINTS.GET_BANNERS),
          api.get<Game[]>(API_ENDPOINTS.GET_ALL_GAMES)
        ]);

        if (bannersResponse.status === 200) {
          setBanners(bannersResponse.data);
        }

        if (gamesResponse.status === 200) {
          setGamesData(gamesResponse.data);
        }
      } catch (gameError: any) {
        console.error("Error fetching games or banners:", gameError);
        
        // If games API requires auth (401), we'll show games section only after login
        if (gameError.response?.status === 401) {
          console.log('Games API requires authentication - games will show after login');
        }
        // Don't set the main error state here, as we still want to try fetching tournaments
      }
      
      // Fetch tournaments separately so authorization issues don't block the whole page
      try {
        const tournamentsResponse = await api.get<{ data: TournamentCategory[] }>(
          API_ENDPOINTS.GET_ALL_TOURNAMENTS
        );
        
        if (tournamentsResponse.status === 200) {
          setTournamentsData(tournamentsResponse.data.data);
        }
      } catch (tournamentError) {
        console.error("Error fetching tournaments:", tournamentError);
        // Only log the error, don't prevent the page from loading
      }
    } catch (error) {
      console.error("General error in fetchInitialData:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSocialIcons = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.SOCIAL_ICONS);
      if (response.status === 200) {
        setSocialIcons(response?.data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // Function to refresh games player counts
  const refreshGamesPlayerCounts = useCallback(async () => {
    try {
      const updatedGames = await PlayerCountService.getAllGamesWithPlayerCounts();
      setGamesData(updatedGames);
    } catch (error) {
      console.error("Failed to refresh games player counts:", error);
      // Fallback to the original method if the new service fails
      try {
        const gamesResponse = await api.get<Game[]>(API_ENDPOINTS.GET_ALL_GAMES);
        if (gamesResponse.status === 200) {
          setGamesData(gamesResponse.data);
        }
      } catch (fallbackError) {
        console.error("Fallback games fetch also failed:", fallbackError);
      }
    }
  }, []);

  // Set up auto-refresh for games player counts with 10-second interval
  usePlayerCountRefresh(refreshGamesPlayerCounts, {
    interval: 10000, // 10 seconds
    enabled: gamesData.length > 0, // Only enable if we have games data
    refreshOnMount: false, // We'll do initial fetch in useEffect
    pauseOnHidden: true, // Pause when tab is not visible
  });

  useEffect(() => {
    fetchInitialData();
    fetchSocialIcons();
  }, []);

  if (error) {
    return <div className="p-8 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="px-4 py-5 flex flex-col gap-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-[50vh]">
          <Loader />
        </div>
      ) : (
        <>
          <div className="flex gap-10">
            <div className="w-[300px] bg-gradient-to-b from-[#121f28] to-[#1a2c38] px-5 flex flex-col justify-between pb-5 rounded border border-[#c9ff88]">
              <h2 className="text-xl font-bold text-white my-5 flex flex-col">
                <span className="text-5xl">GamyDay</span>
                <span className="text-3xl pl-1 mt-1 ">Earn Every Day!</span>
                <span className="mt-4 pl-1">Play ➔ Earn ➔ Repeat</span>
              </h2>
              <div className="">
                <h3 className="text-lg font-semibold text-white mb-2.5">
                  Social links
                </h3>
                {socialIcons && (
                  <div className="flex gap-4 flex-wrap">
                    {socialIcons.map((icon, index) => (
                      <Link
                        href={icon.link}
                        key={index}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="cursor-pointer bg-slate-400 bg-opacity-30 p-2.5 rounded"
                      >
                        <img src={icon.image} width={23} height={23} alt="" />
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1">
              {banners.length > 0 && (
                <div
                  className={`${
                    isExpanded
                      ? "w-[calc(100vw-760px)]"
                      : "w-[calc(100vw-540px)]"
                  }`}
                >
                  <SliderBanner banners={banners} />
                </div>
              )}
              <GamyDayPartners />
            </div>
          </div>

          {/* Games Section - Show fetched games if available */}
          {gamesData.length > 0 && (
            <GamesSection 
              games={gamesData} 
              title="24/7 - Live Games" 
            />
          )}

          {tournamentsData.map((tournamentCategory, index) => (
            <TournamentSection
              key={`${tournamentCategory.type}-${index}`}
              tournaments={tournamentCategory.tournaments}
              sectionTitle={tournamentCategory.type}
              selectedTournamentId={selectedTournamentId}
              selectedDateFilter={null}
            />
          ))}

          {/* Temporary notification test component */}
          <NotificationTest />
        </>
      )}
    </div>
  );
};

export default withSidebar(Home);
