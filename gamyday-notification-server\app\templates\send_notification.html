{% extends "base.html" %}

{% block title %}Send Notification - GamyDay Notification Admin{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Page Header -->
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900">Send Notification</h2>
        <p class="text-gray-600">Send a push notification to your subscribers</p>
    </div>

    <!-- Notification Form -->
    <div class="bg-white rounded-lg shadow p-6">
        <form method="POST" action="/admin/send-notification" class="space-y-6">
            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-heading mr-2"></i>
                    Title *
                </label>
                <input 
                    type="text" 
                    id="title" 
                    name="title" 
                    required 
                    maxlength="100"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter notification title"
                >
                <p class="text-xs text-gray-500 mt-1">Maximum 100 characters</p>
            </div>

            <!-- Body -->
            <div>
                <label for="body" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-align-left mr-2"></i>
                    Message *
                </label>
                <textarea 
                    id="body" 
                    name="body" 
                    required 
                    rows="4"
                    maxlength="300"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter notification message"
                ></textarea>
                <p class="text-xs text-gray-500 mt-1">Maximum 300 characters</p>
            </div>

            <!-- URL -->
            <div>
                <label for="url" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-link mr-2"></i>
                    Click URL
                </label>
                <input 
                    type="url" 
                    id="url" 
                    name="url" 
                    value="/"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://example.com/page"
                >
                <p class="text-xs text-gray-500 mt-1">URL to open when notification is clicked</p>
            </div>

            <!-- Icon -->
            <div>
                <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-image mr-2"></i>
                    Icon URL
                </label>
                <input 
                    type="url" 
                    id="icon" 
                    name="icon" 
                    value="/favicon.png"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://example.com/icon.png"
                >
                <p class="text-xs text-gray-500 mt-1">Icon to display with the notification</p>
            </div>

            <!-- Target Type -->
            <div>
                <label for="target_type" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-bullseye mr-2"></i>
                    Send To
                </label>
                <select 
                    id="target_type" 
                    name="target_type"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                    <option value="all">All Active Subscribers</option>
                    <option value="recent">Recently Active Subscribers</option>
                </select>
                <p class="text-xs text-gray-500 mt-1">Choose who should receive this notification</p>
            </div>

            <!-- Preview -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-700 mb-3">
                    <i class="fas fa-eye mr-2"></i>
                    Preview
                </h4>
                <div class="bg-white rounded-lg shadow-sm border p-4 max-w-sm">
                    <div class="flex items-start space-x-3">
                        <img id="preview-icon" src="/favicon.png" alt="Icon" class="w-8 h-8 rounded">
                        <div class="flex-1 min-w-0">
                            <p id="preview-title" class="text-sm font-medium text-gray-900">Notification Title</p>
                            <p id="preview-body" class="text-sm text-gray-600 mt-1">Notification message will appear here</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4">
                <a href="/admin/" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </a>
                <button 
                    type="submit" 
                    class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Notification
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    // Live preview functionality
    function updatePreview() {
        const title = document.getElementById('title').value || 'Notification Title';
        const body = document.getElementById('body').value || 'Notification message will appear here';
        const icon = document.getElementById('icon').value || '/favicon.png';
        
        document.getElementById('preview-title').textContent = title;
        document.getElementById('preview-body').textContent = body;
        document.getElementById('preview-icon').src = icon;
    }
    
    // Add event listeners for live preview
    document.getElementById('title').addEventListener('input', updatePreview);
    document.getElementById('body').addEventListener('input', updatePreview);
    document.getElementById('icon').addEventListener('input', updatePreview);
    
    // Character count for title and body
    function addCharacterCount(inputId, maxLength) {
        const input = document.getElementById(inputId);
        const counter = document.createElement('div');
        counter.className = 'text-xs text-gray-400 text-right mt-1';
        input.parentNode.appendChild(counter);
        
        function updateCount() {
            const remaining = maxLength - input.value.length;
            counter.textContent = `${input.value.length}/${maxLength}`;
            counter.className = remaining < 20 ? 'text-xs text-red-500 text-right mt-1' : 'text-xs text-gray-400 text-right mt-1';
        }
        
        input.addEventListener('input', updateCount);
        updateCount();
    }
    
    addCharacterCount('title', 100);
    addCharacterCount('body', 300);
</script>
{% endblock %}
