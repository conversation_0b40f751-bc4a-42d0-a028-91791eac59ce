'use client';

/**
 * Comprehensive Diagnostic Tool for Push Notifications
 * Tests each step of the subscription process individually
 */

import React, { useState, useEffect } from 'react';
import { notificationService } from './service';

interface DiagnosticResult {
  step: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: any;
}

export function DiagnosticTool() {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (step: string, status: 'success' | 'error', message: string, details?: any) => {
    setResults(prev => [...prev, { step, status, message, details }]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    setResults([]);

    // Step 1: Browser Support
    try {
      const isSupported = notificationService.isSupported();
      addResult(
        'Browser Support',
        isSupported ? 'success' : 'error',
        isSupported ? 'All required APIs are supported' : 'Missing required browser APIs',
        {
          serviceWorker: 'serviceWorker' in navigator,
          pushManager: 'PushManager' in window,
          notification: 'Notification' in window,
        }
      );
    } catch (error) {
      addResult('Browser Support', 'error', error.message);
    }

    // Step 2: Permission Status
    try {
      const permission = Notification.permission;
      addResult(
        'Permission Status',
        permission === 'granted' ? 'success' : permission === 'denied' ? 'error' : 'pending',
        `Permission: ${permission}`,
        { permission }
      );
    } catch (error) {
      addResult('Permission Status', 'error', error.message);
    }

    // Step 3: Service Worker Registration
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      await navigator.serviceWorker.ready;
      addResult(
        'Service Worker',
        'success',
        'Service worker registered and ready',
        {
          scope: registration.scope,
          active: !!registration.active,
          installing: !!registration.installing,
          waiting: !!registration.waiting,
        }
      );
    } catch (error) {
      addResult('Service Worker', 'error', `Service worker failed: ${error.message}`);
    }

    // Step 4: Backend Health
    try {
      const response = await fetch('https://gamyday-notification.onrender.com/health');
      const data = await response.json();
      addResult(
        'Backend Health',
        response.ok ? 'success' : 'error',
        response.ok ? 'Backend is healthy' : 'Backend health check failed',
        data
      );
    } catch (error) {
      addResult('Backend Health', 'error', `Backend unreachable: ${error.message}`);
    }

    // Step 5: VAPID Key Fetch
    try {
      const response = await fetch('https://gamyday-notification.onrender.com/api/vapid-public-key');
      const data = await response.json();
      const cleanKey = data.public_key.replace(/\\_/g, '_');
      addResult(
        'VAPID Key',
        response.ok ? 'success' : 'error',
        response.ok ? 'VAPID key fetched successfully' : 'VAPID key fetch failed',
        {
          keyLength: cleanKey.length,
          keyPreview: cleanKey.substring(0, 20) + '...',
          rawKey: data.public_key,
          cleanedKey: cleanKey,
        }
      );
    } catch (error) {
      addResult('VAPID Key', 'error', `VAPID key fetch failed: ${error.message}`);
    }

    // Step 6: VAPID Key Conversion
    try {
      const response = await fetch('https://gamyday-notification.onrender.com/api/vapid-public-key');
      const data = await response.json();
      const cleanKey = data.public_key.replace(/\\_/g, '_');
      
      // Test VAPID key conversion
      const padding = '='.repeat((4 - (cleanKey.length % 4)) % 4);
      const base64 = (cleanKey + padding).replace(/-/g, '+').replace(/_/g, '/');
      const rawData = window.atob(base64);
      const uint8Array = new Uint8Array(rawData.length);
      
      for (let i = 0; i < rawData.length; ++i) {
        uint8Array[i] = rawData.charCodeAt(i);
      }
      
      addResult(
        'VAPID Conversion',
        'success',
        'VAPID key converted successfully',
        {
          originalLength: cleanKey.length,
          paddedLength: base64.length,
          decodedLength: rawData.length,
          uint8ArrayLength: uint8Array.length,
        }
      );
    } catch (error) {
      addResult('VAPID Conversion', 'error', `VAPID key conversion failed: ${error.message}`);
    }

    // Step 7: Push Manager Check
    try {
      const registration = await navigator.serviceWorker.ready;
      const pushManager = registration.pushManager;
      
      if (!pushManager) {
        throw new Error('Push manager not available');
      }
      
      const permissionState = await pushManager.permissionState({
        userVisibleOnly: true,
      });
      
      addResult(
        'Push Manager',
        'success',
        'Push manager is available',
        {
          available: !!pushManager,
          permissionState,
          supportedContentEncodings: pushManager.supportedContentEncodings || 'Not available',
        }
      );
    } catch (error) {
      addResult('Push Manager', 'error', `Push manager check failed: ${error.message}`);
    }

    // Step 8: Test Subscription Creation
    try {
      if (Notification.permission !== 'granted') {
        addResult('Subscription Test', 'error', 'Cannot test subscription: permission not granted');
      } else {
        const registration = await navigator.serviceWorker.ready;
        const response = await fetch('https://gamyday-notification.onrender.com/api/vapid-public-key');
        const data = await response.json();
        const cleanKey = data.public_key.replace(/\\_/g, '_');
        
        // Convert VAPID key
        const padding = '='.repeat((4 - (cleanKey.length % 4)) % 4);
        const base64 = (cleanKey + padding).replace(/-/g, '+').replace(/_/g, '/');
        const rawData = window.atob(base64);
        const applicationServerKey = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
          applicationServerKey[i] = rawData.charCodeAt(i);
        }
        
        // Try to create subscription
        const subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: applicationServerKey,
        });
        
        addResult(
          'Subscription Test',
          'success',
          'Push subscription created successfully',
          {
            endpoint: subscription.endpoint.substring(0, 50) + '...',
            p256dhKey: subscription.getKey('p256dh') ? 'Present' : 'Missing',
            authKey: subscription.getKey('auth') ? 'Present' : 'Missing',
          }
        );
      }
    } catch (error) {
      addResult('Subscription Test', 'error', `Subscription creation failed: ${error.message}`, {
        errorName: error.name,
        errorStack: error.stack,
      });
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      default: return '⚪';
    }
  };

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-blue-800">🔧 Push Notification Diagnostics</h3>
        <button
          onClick={runDiagnostics}
          disabled={isRunning}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'Running...' : 'Run Diagnostics'}
        </button>
      </div>

      {results.length > 0 && (
        <div className="space-y-3">
          {results.map((result, index) => (
            <div key={index} className="bg-white rounded p-3 border">
              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {getStatusIcon(result.status)} {result.step}
                </span>
                <span className={`text-sm ${
                  result.status === 'success' ? 'text-green-600' : 
                  result.status === 'error' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {result.message}
                </span>
              </div>
              
              {result.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-gray-600">Details</summary>
                  <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      )}

      <div className="mt-4 text-sm text-blue-700">
        <p><strong>Instructions:</strong></p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Click "Run Diagnostics" to test each step of the notification system</li>
          <li>Look for any ❌ errors that indicate where the problem is</li>
          <li>Check the "Details" sections for technical information</li>
          <li>Share the results to help identify the exact issue</li>
        </ul>
      </div>
    </div>
  );
}
