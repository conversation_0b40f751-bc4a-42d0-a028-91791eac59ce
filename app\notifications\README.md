# GamyDay Notification System

A complete web push notification system for the GamyDay frontend application. This system is designed to be modular, self-contained, and easily removable.

## Features

- 🔔 **Web Push Notifications**: Full support for browser push notifications
- 🔐 **Permission Management**: Automatic and manual permission request handling
- 🔧 **Service Worker**: Background notification handling and user interaction
- 📱 **Cross-Browser Support**: Works on all modern browsers that support push notifications
- 🎯 **Modular Design**: Self-contained system that can be easily removed
- ⚙️ **Configurable**: Extensive configuration options for customization

## Architecture

The notification system consists of several key components:

### Core Files

- **`types.ts`** - TypeScript type definitions
- **`config.ts`** - Configuration constants and settings
- **`service.ts`** - Core notification service with all business logic
- **`NotificationProvider.tsx`** - React context provider for state management
- **`NotificationPermission.tsx`** - UI component for permission requests
- **`index.ts`** - Central export file
- **`README.md`** - This documentation

### Service Worker

- **`/public/sw.js`** - Service worker for handling push notifications

## Integration

The system is integrated into the app with minimal changes to existing code:

1. **RootLayout.tsx** - Added `NotificationProvider` and `NotificationPermission` components
2. **Service Worker** - Added to public directory for background processing

## Configuration

### Environment Variables

Set the notification server URL (optional, defaults to localhost:8001):

```env
NEXT_PUBLIC_NOTIFICATION_SERVER_URL=http://localhost:8001
```

### Configuration Options

Edit `config.ts` to customize behavior:

- **Auto-request permission**: Automatically request permission on page load
- **UI display**: Show/hide permission request UI
- **Retry settings**: Configure retry attempts and delays
- **Default notification options**: Set default icons, badges, etc.

## Usage

### Basic Usage

The notification system works automatically once integrated. It will:

1. Initialize on page load
2. Request permission (if configured)
3. Register service worker
4. Subscribe to push notifications
5. Handle incoming notifications

### Using the Hook

```tsx
import { useNotifications } from '@/app/notifications';

function MyComponent() {
  const {
    permissionState,
    requestPermission,
    subscribe,
    unsubscribe,
    isLoading,
    error
  } = useNotifications();

  const handleEnableNotifications = async () => {
    const granted = await requestPermission();
    if (granted) {
      await subscribe();
    }
  };

  return (
    <div>
      <p>Permission: {permissionState.permission}</p>
      <p>Subscribed: {permissionState.isSubscribed ? 'Yes' : 'No'}</p>
      <button onClick={handleEnableNotifications}>
        Enable Notifications
      </button>
    </div>
  );
}
```

### Sending Notifications

Notifications are sent from the backend server. The frontend automatically handles:

- Displaying notifications
- User interactions (clicks, dismissals)
- Navigation on notification click

## API Integration

The system communicates with the notification server at these endpoints:

- **GET** `/api/vapid-public-key` - Get VAPID public key
- **POST** `/api/subscribe` - Subscribe to notifications
- **POST** `/api/unsubscribe` - Unsubscribe from notifications
- **POST** `/api/notifications/send` - Send notification (backend only)

## Browser Support

- ✅ Chrome 50+
- ✅ Firefox 44+
- ✅ Safari 16+
- ✅ Edge 17+
- ❌ Internet Explorer (not supported)

## Security

- Uses VAPID keys for secure communication
- Validates all subscription data
- Handles permission states properly
- Secure service worker implementation

## Troubleshooting

### Common Issues

1. **Notifications not working**
   - Check browser support
   - Verify HTTPS (required for production)
   - Check notification permissions
   - Verify service worker registration

2. **Permission denied**
   - User must manually enable in browser settings
   - Clear browser data and try again
   - Check if site is blocked in browser settings

3. **Service worker errors**
   - Check browser console for errors
   - Verify service worker file is accessible
   - Check for HTTPS requirement

### Debug Mode

Enable debug logging by opening browser console. The system logs all operations for debugging.

## Removal Instructions

To completely remove the notification system:

1. **Remove the notifications directory**:
   ```bash
   rm -rf app/notifications
   ```

2. **Remove service worker**:
   ```bash
   rm public/sw.js
   ```

3. **Update RootLayout.tsx**:
   - Remove the import: `import { NotificationProvider, NotificationPermission } from "./notifications";`
   - Remove `<NotificationProvider>` wrapper
   - Remove `<NotificationPermission>` component

4. **Clean up any custom usage**:
   - Remove any `useNotifications()` hooks from components
   - Remove any notification-related imports

The system is designed to be completely self-contained, so removing these files will cleanly remove all notification functionality.

## Development

### Testing

1. Start the notification server:
   ```bash
   cd gamyday-notification-server
   python main.py
   ```

2. Start the frontend:
   ```bash
   npm run dev
   ```

3. Open browser and test:
   - Permission request should appear
   - Check browser console for logs
   - Test notification sending from server admin panel

### Customization

- Modify `config.ts` for behavior changes
- Update `NotificationPermission.tsx` for UI changes
- Extend `service.ts` for additional functionality
- Customize `sw.js` for notification handling

## License

This notification system is part of the GamyDay project and follows the same license terms.
