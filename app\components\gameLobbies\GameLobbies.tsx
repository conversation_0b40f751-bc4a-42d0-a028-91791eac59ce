"use client";
import React, { useState, useEffect } from 'react';
import { Game, Lobby, CreateGameBookingRequest } from '@/app/types/CommonComponent.types';
import { GamesService } from '@/app/services/gamesService';
import { MockGamesService } from '@/app/services/mockGamesService';
import Loader from '../common/Loader';
import { toast } from 'react-toastify';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface GameLobbiesProps {
  game: Game;
  onBack: () => void;
  onBookingSuccess?: () => void;
}

const GameLobbies: React.FC<GameLobbiesProps> = ({ game, onBack, onBookingSuccess }) => {
  const [lobbies, setLobbies] = useState<Lobby[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isBooking, setIsBooking] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchLobbies();
  }, [game.game_id]);

  const fetchLobbies = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      try {
        // Try real API first
        const lobbyData = await GamesService.getGameLobbies(game.game_id);
        setLobbies(lobbyData);
      } catch (error) {
        // If real API fails, use mock data
        console.log('Real API not available, using mock data:', error);
        const mockLobbies = await MockGamesService.getGameLobbies(game.game_id);
        setLobbies(mockLobbies);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch lobbies';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinLobby = async (lobby: Lobby) => {
    if (!lobby.is_joinable) {
      toast.error('This lobby is not available for joining');
      return;
    }

    try {
      setIsBooking(true);
      const bookingData: CreateGameBookingRequest = {
        game_id: game.game_id,
        lobby_id: lobby.lobby_id,
        game_name: game.name,
        return_to: window.location.origin
      };

      try {
        // Try real API first
        await GamesService.createGameBooking(bookingData);
        toast.success('Game booking created successfully!');
      } catch (error) {
        // If real API fails, use mock service
        console.log('Real API not available, using mock data:', error);
        await MockGamesService.createGameBooking(bookingData);
        toast.success('Game booking created successfully! (Demo Mode)');
      }
      
      if (onBookingSuccess) {
        onBookingSuccess();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create booking';
      toast.error(errorMessage);
    } finally {
      setIsBooking(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'waiting': return 'text-yellow-400 bg-yellow-400/20';
      case 'active': return 'text-green-400 bg-green-400/20';
      case 'completed': return 'text-gray-400 bg-gray-400/20';
      case 'cancelled': return 'text-red-400 bg-red-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <Loader />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-[#c9ff88] hover:text-white transition-colors"
        >
          <ArrowLeftIcon className="w-5 h-5" />
          <span>Back to Games</span>
        </button>
        <div className="flex-1">
          <h2 className="text-2xl font-bold text-white">{game.name} - Available Lobbies</h2>
        </div>
      </div>

      {error && (
        <div className="text-red-500 text-center p-4">
          <p>Error: {error}</p>
          <button 
            onClick={fetchLobbies}
            className="mt-2 px-4 py-2 bg-[#c9ff88] text-black rounded hover:bg-[#b8e877] transition-colors"
          >
            Retry
          </button>
        </div>
      )}

      {lobbies.length === 0 && !error ? (
        <div className="text-gray-400 text-center p-8">
          <p>No lobbies available for this game at the moment.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {lobbies.map((lobby) => (
            <div
              key={lobby.lobby_id}
              className="bg-gradient-to-b from-[#1a2c38] to-[#121f28] border border-[#2a4451] rounded-lg p-4 hover:border-[#c9ff88] transition-colors"
            >
              <div className="flex justify-between items-start mb-3">
                <h3 className="text-white font-bold text-lg">{lobby.name}</h3>
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(lobby.status)}`}>
                  {lobby.status.charAt(0).toUpperCase() + lobby.status.slice(1)}
                </span>
              </div>

              {lobby.description && (
                <p className="text-gray-400 text-sm mb-3">{lobby.description}</p>
              )}

              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Active Players:</span>
                  <span className="text-white">{lobby.active_player_count !== undefined ? lobby.active_player_count : `${lobby.current_players}/${lobby.max_players}`}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Entry Fee:</span>
                  <span className="text-[#c9ff88] font-semibold">₹{lobby.entry_fee}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Prize Pool:</span>
                  <span className="text-[#c9ff88] font-semibold">₹{lobby.prize_pool}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Start Time:</span>
                  <span className="text-white">{new Date(lobby.start_time).toLocaleString()}</span>
                </div>
              </div>

              <button
                onClick={() => handleJoinLobby(lobby)}
                disabled={!lobby.is_joinable || isBooking}
                className={`w-full py-2 px-4 rounded font-semibold transition-colors ${
                  lobby.is_joinable && !isBooking
                    ? 'bg-[#c9ff88] text-black hover:bg-[#b8e877]'
                    : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                }`}
              >
                {isBooking ? 'Joining...' : lobby.is_joinable ? 'Join Lobby' : 'Not Available'}
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default GameLobbies;
