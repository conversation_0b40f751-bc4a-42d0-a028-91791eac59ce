"use client";
import React from "react";
import { Game, GamesSectionProps } from "@/app/types/Game";
import Link from "next/link";

const GamesSection: React.FC<GamesSectionProps> = ({ games, title }) => {
  if (!games || games.length === 0) {
    return null;
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-white">{title}</h2>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {games.map((game) => (
          <GameCard key={game.id} game={game} />
        ))}
      </div>
    </div>
  );
};

const GameCard: React.FC<{ game: Game }> = ({ game }) => {
  return (
    <Link href={`/game-details?game_id=${game.id}`} className="block">
      <div className="bg-[#141517] rounded-lg border border-[#707070] overflow-hidden hover:border-[#c9ff88] transition-all duration-300 h-full">
        <div className="relative w-40 h-40">
          <img
            src={game.image || `/icons/${game.game_name.toLowerCase().replace(/\s+/g, '')}.png`}
            alt={game.game_name}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/icons/tictactoelive.png'; // Fallback image
            }}
          />
        </div>
        <div className="p-4">
          <h3 className="text-lg font-semibold text-white mb-1">{game.game_name}</h3>
          
          {/* Active Player Count */}
          {game.active_player_count !== undefined && (
            <div className="flex items-center text-xs text-green-400 mb-2">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
              <span>{game.active_player_count} active players</span>
            </div>
          )}
          
          <div className="mt-3">
            <button className="w-full bg-[#c9ff88] text-[#070b28] py-2 rounded-md font-semibold hover:bg-opacity-90 transition-all duration-300">
              Play Now
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default GamesSection;
