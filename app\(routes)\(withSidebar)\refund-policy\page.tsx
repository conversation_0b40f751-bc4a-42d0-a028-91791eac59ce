import React from "react";

const RefundPolicy = () => {
  return (
    <div className="p-10 space-y-8 text-white">
      <h1 className="text-3xl font-bold mb-6">Refund Policy</h1>

      <p className="text-lg font-medium">
        At GamyDay, we strive to provide a fair and transparent refund process while maintaining the integrity of our platform. Please read the following terms carefully:
      </p>

      <div>
        <h2 className="text-2xl font-bold mb-4">1. Eligibility for Refunds</h2>
        <ul className="list-disc pl-6 space-y-2 text-lg font-medium">
          <li>
            Refunds are processed <em>only for tournaments officially canceled</em> by GamyDay.
          </li>
        </ul>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">2. How to Request a Refund</h2>
        <ul className="list-disc pl-6 space-y-2 text-lg font-medium">
          <li>
            Users must raise a refund request via the <em>&apos;Raise a Ticket&apos;</em> feature available on the platform.
          </li>
          <li>
            The request must include:
            <ul className="list-disc pl-6 space-y-1">
              <li>Valid payment details</li>
              <li>Relevant tournament information</li>
            </ul>
          </li>
        </ul>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">3. Refund Method</h2>
        <ul className="list-disc pl-6 space-y-2 text-lg font-medium">
          <li>
            Approved refunds will be issued to the <em>original payment method</em> used during the transaction.
          </li>
        </ul>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">4. Non-Refundable Scenarios</h2>
        <ul className="list-disc pl-6 space-y-2 text-lg font-medium">
          <li>
            No refunds will be issued in the following cases:
            <ul className="list-disc pl-6 space-y-1">
              <li>
                User fails to join due to <em>personal error, late entry, or technical issues not caused by GamyDay</em>
              </li>
              <li>
                Missed participation due to:
                <ul className="list-disc pl-6 space-y-1">
                  <li>Internet failure</li>
                  <li>Device compatibility issues</li>
                  <li>User error</li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">5. Processing Time</h2>
        <ul className="list-disc pl-6 space-y-2 text-lg font-medium">
          <li>
            Once approved, <em>refunds will be processed within 24 hours</em> of the request.
          </li>
        </ul>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">6. Wallet System</h2>
        <ul className="list-disc pl-6 space-y-2 text-lg font-medium">
          <li>
            GamyDay uses a dual-wallet system to manage user funds:
            <ul className="list-disc pl-6 space-y-1">
              <li>
                <strong>Green Wallet:</strong> All deposits made by players are added to the Green Wallet. These funds can only be used for participating in GamyDay tournaments and other GamyDay activities. <strong>This wallet is non-refundable and non-withdrawable.</strong>
              </li>
              <li>
                <strong>Red Wallet:</strong> All winnings earned through tournaments are credited to the Red Wallet. Players can redeem or withdraw funds from this wallet, subject to verification and withdrawal policies.
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default RefundPolicy;
