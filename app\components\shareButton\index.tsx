"use client";
import React, { useState } from 'react';
import { ShareIcon } from '@heroicons/react/24/outline';

interface ShareButtonProps {
  url: string;
  title: string;
  text?: string;
  className?: string;
  iconClassName?: string;
  position?: 'absolute' | 'relative';
  top?: string;
  left?: string;
  right?: string;
  bottom?: string;
}

const ShareButton: React.FC<ShareButtonProps> = ({
  url,
  title,
  text = 'Check out this tournament!',
  className = '',
  iconClassName = '',
  position = 'absolute',
  top = '10px',
  left = '10px',
  right = 'auto',
  bottom = 'auto',
}) => {
  const [copied, setCopied] = useState(false);

  const handleShare = async () => {
    // Check if the Web Share API is supported
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text,
          url,
        });
      } catch (error) {
        console.error('Error sharing:', error);
        // Fallback to clipboard if sharing fails
        copyToClipboard();
      }
    } else {
      // Fallback for browsers that don't support the Web Share API
      copyToClipboard();
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(url)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((error) => {
        console.error('Error copying to clipboard:', error);
      });
  };

  // Create position style object based on props
  const positionStyle = {
    position: position,
    top: top,
    left: left,
    right: right !== 'auto' ? right : undefined,
    bottom: bottom !== 'auto' ? bottom : undefined,
  };

  return (
    <div 
      className={`z-10 ${className}`}
      style={positionStyle}
    >
      <button
        onClick={handleShare}
        className={`bg-[#c9ff88] text-[#070b28] p-2 rounded-full hover:bg-[#a7f04e] transition-colors ${iconClassName}`}
        title="Share tournament"
      >
        <ShareIcon className="h-5 w-5" />
        {copied && (
          <div className="absolute -bottom-8 left-0 bg-black bg-opacity-75 text-white text-xs py-1 px-2 rounded whitespace-nowrap">
            Link copied!
          </div>
        )}
      </button>
    </div>
  );
};

export default ShareButton;