# Environment variables for GamyDay Notification Server

# MongoDB Configuration
MONGODB_URL=mongodb+srv://username:<EMAIL>/gamyday_notifications?retryWrites=true&w=majority

# VAPID Keys for Web Push (Generate your own keys)
VAPID_PUBLIC_KEY=BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HnKJuOmLWjMpS_7QX0-SJl6FxOaujKy6yOLXmYWNWBdXYFOuY
VAPID_PRIVATE_KEY=your-vapid-private-key-here
VAPID_SUBJECT=mailto:<EMAIL>

# Server Configuration
PORT=8000
HOST=0.0.0.0
DEBUG=True

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://your-frontend-domain.com

# Admin Panel Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password-here

# Security
SECRET_KEY=your-secret-key-for-sessions
