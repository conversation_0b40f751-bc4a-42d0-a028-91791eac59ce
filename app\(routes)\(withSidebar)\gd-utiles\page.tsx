import Link from "next/link";
import Image from "next/image";

const GdUtilsPage = () => {
  const utils = [
    { name: "AI Player Picker", href: "https://valorsp.onrender.com", description: "Randomly picks players for your custom games using AI.", imageSrc: "/icons/playerpicker.png" },
    { name: "<PERSON><PERSON> Gallop", href: "https://horse-race-44h9.onrender.com/", description: "Tap the green dots as fast as you can to power your horse to victory! 🐎.", imageSrc: "/icons/tapgallop.png" },
    { name: "Battle Bots", href: "https://gitesh-vyrix-game.onrender.com/", description: "Free to Play fun Battle Bot games.", imageSrc: "/icons/battlebots.png" },
    { name: "Snake'n'Ladder", href: "https://saanp.onrender.com/", description: "Free to Play fun Classic Snake'n'Ladder.", imageSrc: "/icons/snakenladder.png" },
    { name: "<PERSON> Boom", href: "https://luckyboomer.onrender.com/", description: "Saves Your Minecraft Co-ordinates.", imageSrc: "/icons/luckyboom.png" },
    { name: "Rock Paper Scissors", href: "https://rockpaperscissorr.onrender.com/", description: "Classic Rock Paper Scissors Game.", imageSrc: "/icons/rockpaper.png" },
    { name: "Dot & Boxes", href: "https://tourmaline-hummingbird-8d3774.netlify.app/", description: "Classic Dot & Box Game.", imageSrc: "/icons/dot&box.png" },
    { name: "Tic 4 Toe", href: "https://papaya-syrniki-292ec3.netlify.app/", description: "Free to Play fun classic games Connect 4 with twist of tic tac toe.", imageSrc: "/icons/tic4toe.png" },
    { name: "Valorant Strategy Board", href: "https://valorantsb.onrender.com/", description: "Create strategy with you friends and dominate the games.", imageSrc: "/icons/strategyboard.png" },
    { name: "Two-Games", href: "https://two-games.onrender.com/index.html", description: "Free to Play fun classic games.", imageSrc: "/icons/twogames.png" },
    { name: "Minecraft Location Tracker", href: "https://minecraftlt.onrender.com/", description: "Saves Your Minecraft Co-ordinates.", imageSrc: "/icons/minecraftlt.png" },
  ];

  return (
    <div className="p-6 bg-gradient-to-br from-[#1a2c38] to-[#1a2c38] min-h-screen text-white">
      <h1 className="text-4xl font-bold mb-8 text-[#c9ff88]">Gamix.io by GamyDay</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {utils.map((util) => (
          <Link href={util.href} key={util.name} target="_blank" rel="noopener noreferrer">
            <div className="bg-[#1a1d21] p-6 rounded-lg shadow-xl hover:shadow-2xl transition-shadow duration-300 transform hover:-translate-y-1 h-full flex flex-row">
              {util.imageSrc && (
                <div className="flex-shrink-0 mr-4">
                  <Image 
                    src={util.imageSrc} 
                    alt={`${util.name}`} 
                    width={150} 
                    height={150} 
                    className="rounded object-cover" 
                  />
                </div>
              )}
              <div className="flex flex-col justify-between">
                <div>
                  <h2 className="text-2xl font-semibold text-[#c9ff88] mb-2">{util.name}</h2>
                  <p className="text-gray-400 text-sm">{util.description}</p>
                </div>
                <p className="text-xs text-gray-500 mt-4"></p>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default GdUtilsPage;
