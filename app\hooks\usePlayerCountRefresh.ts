"use client";
import { useEffect, useRef, useCallback } from "react";

interface UsePlayerCountRefreshOptions {
  /**
   * Refresh interval in milliseconds
   * @default 10000 (10 seconds)
   */
  interval?: number;
  
  /**
   * Whether the refresh should be active
   * @default true
   */
  enabled?: boolean;
  
  /**
   * Whether to refresh immediately on mount
   * @default false
   */
  refreshOnMount?: boolean;
  
  /**
   * Whether to pause refresh when the tab is not visible
   * @default true
   */
  pauseOnHidden?: boolean;
}

/**
 * Custom hook for automatically refreshing player count data at specified intervals
 * @param refreshFunction - Function to call for refreshing data
 * @param options - Configuration options
 */
export const usePlayerCountRefresh = (
  refreshFunction: () => Promise<void> | void,
  options: UsePlayerCountRefreshOptions = {}
) => {
  const {
    interval = 10000, // 10 seconds default
    enabled = true,
    refreshOnMount = false,
    pauseOnHidden = true,
  } = options;

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isVisibleRef = useRef(true);

  // Memoized refresh function to prevent unnecessary re-renders
  const memoizedRefreshFunction = useCallback(refreshFunction, [refreshFunction]);

  // Handle visibility change
  const handleVisibilityChange = useCallback(() => {
    isVisibleRef.current = !document.hidden;
    
    if (pauseOnHidden) {
      if (document.hidden) {
        // Pause refresh when tab is hidden
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      } else {
        // Resume refresh when tab becomes visible
        if (enabled && !intervalRef.current) {
          intervalRef.current = setInterval(() => {
            if (isVisibleRef.current || !pauseOnHidden) {
              memoizedRefreshFunction();
            }
          }, interval);
        }
      }
    }
  }, [enabled, interval, memoizedRefreshFunction, pauseOnHidden]);

  // Start the refresh interval
  const startRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    intervalRef.current = setInterval(() => {
      if (isVisibleRef.current || !pauseOnHidden) {
        memoizedRefreshFunction();
      }
    }, interval);
  }, [interval, memoizedRefreshFunction, pauseOnHidden]);

  // Stop the refresh interval
  const stopRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Manual refresh function
  const manualRefresh = useCallback(async () => {
    try {
      await memoizedRefreshFunction();
    } catch (error) {
      console.error("Manual refresh failed:", error);
    }
  }, [memoizedRefreshFunction]);

  useEffect(() => {
    if (enabled) {
      // Refresh immediately on mount if requested
      if (refreshOnMount) {
        memoizedRefreshFunction();
      }
      
      // Start the interval
      startRefresh();
      
      // Add visibility change listener
      if (pauseOnHidden) {
        document.addEventListener("visibilitychange", handleVisibilityChange);
      }
    }

    return () => {
      stopRefresh();
      if (pauseOnHidden) {
        document.removeEventListener("visibilitychange", handleVisibilityChange);
      }
    };
  }, [enabled, startRefresh, stopRefresh, handleVisibilityChange, pauseOnHidden, memoizedRefreshFunction, refreshOnMount]);

  return {
    manualRefresh,
    startRefresh,
    stopRefresh,
    isActive: !!intervalRef.current,
  };
};
