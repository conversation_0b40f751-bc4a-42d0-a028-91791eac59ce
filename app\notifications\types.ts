/**
 * TypeScript types for the notification system
 */

export interface PushSubscriptionKeys {
  p256dh: string;
  auth: string;
}

export interface PushSubscriptionData {
  endpoint: string;
  keys: PushSubscriptionKeys;
}

export interface NotificationSubscription {
  subscription: PushSubscriptionData;
  timestamp?: string;
  user_agent?: string;
  ip_address?: string;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  data?: Record<string, any>;
  actions?: NotificationAction[];
  tag?: string;
  require_interaction?: boolean;
  url?: string;
}

export interface NotificationRequest {
  payload: NotificationPayload;
  target_type?: 'all' | 'specific' | 'recent';
  target_ids?: string[];
  schedule_time?: string;
}

export interface NotificationPermissionState {
  permission: NotificationPermission;
  isSupported: boolean;
  isSubscribed: boolean;
  subscription: PushSubscription | null;
}

export interface NotificationServiceConfig {
  serverUrl: string;
  vapidPublicKey: string;
  swPath: string;
}

export interface NotificationContextType {
  permissionState: NotificationPermissionState;
  requestPermission: () => Promise<boolean>;
  subscribe: () => Promise<boolean>;
  unsubscribe: () => Promise<boolean>;
  isLoading: boolean;
  error: string | null;
}

export interface ServiceWorkerMessage {
  type: 'NOTIFICATION_CLICK' | 'NOTIFICATION_CLOSE' | 'SUBSCRIPTION_UPDATE';
  data?: any;
}
