"use client";
import React from "react";
import { Game, GamesSectionProps } from "@/app/types/Game";
import Link from "next/link";

const GamesSection: React.FC<GamesSectionProps> = ({ games, title }) => {
  if (!games || games.length === 0) {
    return null;
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-white">{title}</h2>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4 auto-rows-fr">
        {games.map((game) => (
          <GameCard key={game.id} game={game} />
        ))}
      </div>
    </div>
  );
};

const GameCard: React.FC<{ game: Game }> = ({ game }) => {
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = e.target as HTMLImageElement;
    target.src = '/icons/twogames.png';
  };

  return (
    <Link href={`/game-details?game_id=${game.id}`} className="block h-full">
      <div className="bg-gradient-to-b from-[#1a2c38] to-[#121f28] border border-[#c9ff88] rounded-lg overflow-hidden hover:border-[#c9ff88] hover:shadow-lg hover:shadow-[#c9ff88]/20 transition-all duration-300 h-full group">
        {/* Game Image with square aspect ratio */}
        <div className="relative w-full aspect-square bg-gray-800 overflow-hidden">
          <img
            src={game.image || `/icons/${game.game_name.toLowerCase().replace(/\s+/g, '')}.png`}
            alt={game.game_name}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
            onError={handleImageError}
          />
          
          {/* Status Badge - Show if is_active property exists */}
          {/* {game.is_active !== undefined && (
            <div className="absolute top-2 right-2">
              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                game.is_active 
                  ? 'bg-green-500 text-white' 
                  : 'bg-red-500 text-white'
              }`}>
                {game.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          )} */}
        </div>
        
        {/* Game Info with balanced proportions */}
        <div className="p-2 flex flex-col justify-between flex-1">
          <div>
            <h3 className="text-sm font-bold text-white mb-1 group-hover:text-[#c9ff88] transition-colors duration-300 line-clamp-1">{game.game_name}</h3>
            
            {/* Active Player Count */}
            {game.active_player_count !== undefined && (
              <div className="flex items-center text-xs text-green-400 mb-1">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                <span>{game.active_player_count} active players</span>
              </div>
            )}
            
            {/* Game description */}
            {/* <div 
              className="text-gray-400 text-xs overflow-hidden mb-2"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                minHeight: '2rem'
              } as React.CSSProperties}
            >
              {game.game_desc}
            </div> */}
          </div>
          
          <div className="mt-auto">
            <button className="w-full bg-[#c9ff88] text-[#070b28] py-1 rounded-md font-semibold hover:bg-opacity-90 transition-all duration-300 group-hover:shadow-md text-xs">
              Play Now
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default GamesSection;
