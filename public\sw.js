/**
 * Service Worker for GamyDay Push Notifications
 * Handles incoming push notifications and user interactions
 */

const CACHE_NAME = 'gamyday-notifications-v1';
const DEFAULT_ICON = '/favicon.png';
const DEFAULT_BADGE = '/favicon.png';
const DEFAULT_URL = '/';

// Install event - cache essential resources
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker');
  
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll([
        DEFAULT_ICON,
        DEFAULT_BADGE,
        DEFAULT_URL
      ]);
    })
  );
  
  // Take control immediately
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('[SW] Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Take control of all clients
  self.clients.claim();
});

// Push event - handle incoming push notifications
self.addEventListener('push', (event) => {
  console.log('[SW] Push event received');
  
  let notificationData = {
    title: 'GamyDay',
    body: 'You have a new notification',
    icon: DEFAULT_ICON,
    badge: DEFAULT_BADGE,
    data: {
      url: DEFAULT_URL
    }
  };
  
  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json();
      notificationData = {
        title: pushData.title || notificationData.title,
        body: pushData.body || notificationData.body,
        icon: pushData.icon || notificationData.icon,
        badge: pushData.badge || notificationData.badge,
        image: pushData.image,
        tag: pushData.tag,
        requireInteraction: pushData.require_interaction || false,
        actions: pushData.actions || [],
        data: {
          url: pushData.url || DEFAULT_URL,
          ...pushData.data
        }
      };
    } catch (error) {
      console.error('[SW] Error parsing push data:', error);
    }
  }
  
  // Show notification
  event.waitUntil(
    self.registration.showNotification(notificationData.title, {
      body: notificationData.body,
      icon: notificationData.icon,
      badge: notificationData.badge,
      image: notificationData.image,
      tag: notificationData.tag,
      requireInteraction: notificationData.requireInteraction,
      actions: notificationData.actions,
      data: notificationData.data
    })
  );
});

// Notification click event - handle user clicks on notifications
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.notification);
  
  // Close the notification
  event.notification.close();
  
  // Get the URL to open
  const urlToOpen = event.notification.data?.url || DEFAULT_URL;
  
  // Handle action clicks
  if (event.action) {
    console.log('[SW] Action clicked:', event.action);
    // Handle specific actions here if needed
  }
  
  // Open or focus the app
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
  
  // Send message to client about notification click
  event.waitUntil(
    clients.matchAll().then((clientList) => {
      clientList.forEach((client) => {
        client.postMessage({
          type: 'NOTIFICATION_CLICK',
          data: {
            action: event.action,
            notification: {
              title: event.notification.title,
              body: event.notification.body,
              data: event.notification.data
            }
          }
        });
      });
    })
  );
});

// Notification close event - handle when user dismisses notification
self.addEventListener('notificationclose', (event) => {
  console.log('[SW] Notification closed:', event.notification);
  
  // Send message to client about notification close
  event.waitUntil(
    clients.matchAll().then((clientList) => {
      clientList.forEach((client) => {
        client.postMessage({
          type: 'NOTIFICATION_CLOSE',
          data: {
            notification: {
              title: event.notification.title,
              body: event.notification.body,
              data: event.notification.data
            }
          }
        });
      });
    })
  );
});

// Message event - handle messages from the main thread
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Background sync event (optional - for future use)
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync:', event.tag);
  
  if (event.tag === 'notification-sync') {
    // Handle background sync for notifications
    event.waitUntil(
      // Add background sync logic here if needed
      Promise.resolve()
    );
  }
});

console.log('[SW] Service worker script loaded');
