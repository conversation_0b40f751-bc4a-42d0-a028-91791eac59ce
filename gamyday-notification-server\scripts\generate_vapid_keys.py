#!/usr/bin/env python3
"""
Script to generate VAPID keys for web push notifications.
Run this script to generate new VAPID keys for your notification server.
"""

import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec


def generate_vapid_keys():
    """Generate VAPID public and private keys."""
    # Generate private key
    private_key = ec.generate_private_key(ec.SECP256R1())
    
    # Get private key in DER format
    private_der = private_key.private_bytes(
        encoding=serialization.Encoding.DER,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    # Get public key in DER format
    public_key = private_key.public_key()
    public_der = public_key.public_bytes(
        encoding=serialization.Encoding.DER,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    
    # Convert to base64url format (without padding)
    private_key_b64 = base64.urlsafe_b64encode(private_der).decode('utf-8').rstrip('=')
    public_key_b64 = base64.urlsafe_b64encode(public_der).decode('utf-8').rstrip('=')
    
    return public_key_b64, private_key_b64


def main():
    """Main function to generate and display VAPID keys."""
    print("Generating VAPID keys for web push notifications...")
    print("=" * 60)
    
    try:
        public_key, private_key = generate_vapid_keys()
        
        print("\n✅ VAPID keys generated successfully!")
        print("\n📋 Add these to your .env file:")
        print("-" * 40)
        print(f"VAPID_PUBLIC_KEY={public_key}")
        print(f"VAPID_PRIVATE_KEY={private_key}")
        print(f"VAPID_SUBJECT=mailto:<EMAIL>")
        
        print("\n📋 Add this to your frontend .env file:")
        print("-" * 40)
        print(f"NEXT_PUBLIC_VAPID_PUBLIC_KEY={public_key}")
        
        print("\n⚠️  Important Notes:")
        print("- Keep your private key secret and secure")
        print("- Update VAPID_SUBJECT with your actual email")
        print("- These keys should be the same across all environments")
        print("- If you regenerate keys, all existing subscriptions will become invalid")
        
    except Exception as e:
        print(f"❌ Error generating VAPID keys: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
