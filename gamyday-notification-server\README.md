# GamyDay Notification Server

A zero-cost web push notification server for the GamyDay gaming platform. This server handles push notification subscriptions, stores them in MongoDB, and provides an admin panel for sending notifications to users.

## Features

- 🚀 **Zero-cost deployment** on Vercel, Render, or any Docker-compatible platform
- 📱 **Web Push Notifications** using VAPID protocol
- 🗄️ **MongoDB integration** for storing subscriptions and notification history
- 🎛️ **Admin panel** with beautiful UI for managing notifications
- 🔒 **Secure authentication** for admin access
- 📊 **Analytics and statistics** for tracking notification performance
- 🌐 **CORS support** for cross-origin requests
- 🐳 **Docker support** for easy deployment

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd gamyday-notification-server
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# MongoDB Configuration
MONGODB_URL=mongodb+srv://username:<EMAIL>/gamyday_notifications

# VAPID Keys (generate using the script)
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_SUBJECT=mailto:<EMAIL>

# Server Configuration
PORT=8000
HOST=0.0.0.0
DEBUG=True

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://your-frontend-domain.com

# Admin Panel Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password

# Security
SECRET_KEY=your-secret-key-for-sessions
```

### 3. Generate VAPID Keys

```bash
python scripts/generate_vapid_keys.py
```

Copy the generated keys to your `.env` file.

### 4. Setup Database

```bash
python scripts/setup_database.py
```

### 5. Run the Server

```bash
python main.py
```

The server will be available at `http://localhost:8000`

## Admin Panel

Access the admin panel at `http://localhost:8000/admin/`

Default credentials:
- Username: `admin`
- Password: `admin123` (change this in production!)

### Admin Features

- **Dashboard**: View statistics and recent notifications
- **Send Notifications**: Create and send push notifications
- **Subscriptions**: View and manage user subscriptions
- **Notification History**: View all sent notifications

## API Endpoints

### Public Endpoints

- `GET /health` - Health check
- `POST /api/subscribe` - Subscribe to notifications
- `POST /api/unsubscribe` - Unsubscribe from notifications
- `GET /api/vapid-public-key` - Get VAPID public key

### Admin Endpoints

- `POST /api/notifications/send` - Send notification (requires auth)
- `GET /api/stats` - Get statistics (requires auth)

### Admin Panel Routes

- `GET /admin/` - Admin dashboard
- `GET /admin/send-notification` - Send notification form
- `GET /admin/subscriptions` - View subscriptions
- `GET /admin/notifications` - View notification history

## Deployment

### Vercel Deployment

1. Install Vercel CLI: `npm i -g vercel`
2. Login: `vercel login`
3. Deploy: `vercel --prod`
4. Set environment variables in Vercel dashboard

### Render Deployment

1. Connect your GitHub repository to Render
2. Use the provided `render.yaml` configuration
3. Set environment variables in Render dashboard

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build and run manually
docker build -t gamyday-notification-server .
docker run -p 8000:8000 --env-file .env gamyday-notification-server
```

### Railway Deployment

1. Install Railway CLI: `npm i -g @railway/cli`
2. Login: `railway login`
3. Deploy: `railway up`

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `MONGODB_URL` | MongoDB connection string | Yes | - |
| `VAPID_PUBLIC_KEY` | VAPID public key for push notifications | Yes | - |
| `VAPID_PRIVATE_KEY` | VAPID private key for push notifications | Yes | - |
| `VAPID_SUBJECT` | VAPID subject (email) | Yes | - |
| `PORT` | Server port | No | 8000 |
| `HOST` | Server host | No | 0.0.0.0 |
| `DEBUG` | Debug mode | No | True |
| `ALLOWED_ORIGINS` | Comma-separated list of allowed origins | No | http://localhost:3000 |
| `ADMIN_USERNAME` | Admin panel username | No | admin |
| `ADMIN_PASSWORD` | Admin panel password | No | admin123 |
| `SECRET_KEY` | Secret key for sessions | No | auto-generated |

## MongoDB Setup

### MongoDB Atlas (Recommended)

1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Create a database user
4. Get the connection string
5. Add your IP address to the whitelist

### Local MongoDB

```bash
# Install MongoDB
# Ubuntu/Debian
sudo apt-get install mongodb

# macOS
brew install mongodb-community

# Start MongoDB
sudo systemctl start mongodb  # Linux
brew services start mongodb-community  # macOS
```

## Security Considerations

- Change default admin credentials
- Use strong VAPID keys
- Set up proper CORS origins
- Use HTTPS in production
- Keep dependencies updated
- Monitor logs for suspicious activity

## Troubleshooting

### Common Issues

1. **VAPID Keys Not Working**
   - Regenerate keys using the provided script
   - Ensure keys are properly set in environment variables

2. **MongoDB Connection Issues**
   - Check connection string format
   - Verify network access and IP whitelist
   - Test connection using MongoDB Compass

3. **CORS Errors**
   - Add your frontend domain to `ALLOWED_ORIGINS`
   - Ensure protocol (http/https) matches

4. **Admin Panel Not Loading**
   - Check admin credentials
   - Verify templates directory exists
   - Check server logs for errors

### Logs

View server logs:
```bash
# Local development
python main.py

# Docker
docker-compose logs notification-server

# Vercel
vercel logs
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Frontend Integration

This server is designed to work with the GamyDay frontend notification module. The frontend should be configured with:

```env
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your-vapid-public-key
NEXT_PUBLIC_NOTIFICATION_SERVER_URL=https://your-server-domain.com
```

## Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the logs for error messages
