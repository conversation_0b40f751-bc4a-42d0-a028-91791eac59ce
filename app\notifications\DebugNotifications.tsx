'use client';

/**
 * Debug component to help troubleshoot notification issues
 */

import React, { useEffect, useState } from 'react';
import { notificationService } from './service';

export function DebugNotifications() {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const checkEverything = async () => {
      const info: any = {
        timestamp: new Date().toISOString(),
        isSupported: notificationService.isSupported(),
        permission: 'Notification' in window ? Notification.permission : 'Not available',
        serviceWorkerSupported: 'serviceWorker' in navigator,
        pushManagerSupported: 'PushManager' in window,
        notificationSupported: 'Notification' in window,
      };

      // Check service worker registration
      if ('serviceWorker' in navigator) {
        try {
          const registrations = await navigator.serviceWorker.getRegistrations();
          info.serviceWorkerRegistrations = registrations.length;
          info.serviceWorkerReady = await navigator.serviceWorker.ready;
        } catch (error) {
          info.serviceWorkerError = error.message;
        }
      }

      // Check if service worker file is accessible
      try {
        const response = await fetch('/sw.js');
        info.serviceWorkerFileAccessible = response.ok;
        info.serviceWorkerFileStatus = response.status;
      } catch (error) {
        info.serviceWorkerFileError = error.message;
      }

      // Check backend connectivity
      try {
        const response = await fetch('https://gamyday-notification.onrender.com/health');
        const data = await response.json();
        info.backendHealthy = response.ok;
        info.backendResponse = data;
      } catch (error) {
        info.backendError = error.message;
      }

      // Check VAPID key
      try {
        const response = await fetch('https://gamyday-notification.onrender.com/api/vapid-public-key');
        const data = await response.json();
        info.vapidKeyAvailable = !!data.public_key;
        info.vapidKeyLength = data.public_key?.length;
      } catch (error) {
        info.vapidKeyError = error.message;
      }

      console.log('[DebugNotifications] Debug info:', info);
      setDebugInfo(info);
    };

    checkEverything();
  }, []);

  const initializeManually = async () => {
    console.log('[DebugNotifications] Manual initialization started...');
    try {
      await notificationService.initialize();
      console.log('[DebugNotifications] Manual initialization successful');
      
      // Refresh debug info
      const info = { ...debugInfo };
      info.manualInitSuccess = true;
      info.manualInitTime = new Date().toISOString();
      setDebugInfo(info);
    } catch (error) {
      console.error('[DebugNotifications] Manual initialization failed:', error);
      const info = { ...debugInfo };
      info.manualInitError = error.message;
      info.manualInitTime = new Date().toISOString();
      setDebugInfo(info);
    }
  };

  return (
    <div className="bg-yellow-100 border border-yellow-400 p-4 rounded-lg mt-4">
      <h3 className="text-lg font-bold text-yellow-800 mb-2">🐛 Notification Debug Info</h3>
      
      <div className="space-y-2 text-sm">
        <div><strong>Supported:</strong> {debugInfo.isSupported ? '✅' : '❌'}</div>
        <div><strong>Permission:</strong> {debugInfo.permission}</div>
        <div><strong>Service Worker Support:</strong> {debugInfo.serviceWorkerSupported ? '✅' : '❌'}</div>
        <div><strong>Push Manager Support:</strong> {debugInfo.pushManagerSupported ? '✅' : '❌'}</div>
        <div><strong>Notification Support:</strong> {debugInfo.notificationSupported ? '✅' : '❌'}</div>
        <div><strong>SW Registrations:</strong> {debugInfo.serviceWorkerRegistrations}</div>
        <div><strong>SW File Accessible:</strong> {debugInfo.serviceWorkerFileAccessible ? '✅' : '❌'} ({debugInfo.serviceWorkerFileStatus})</div>
        <div><strong>Backend Healthy:</strong> {debugInfo.backendHealthy ? '✅' : '❌'}</div>
        <div><strong>VAPID Key Available:</strong> {debugInfo.vapidKeyAvailable ? '✅' : '❌'} ({debugInfo.vapidKeyLength} chars)</div>
        
        {debugInfo.serviceWorkerError && (
          <div className="text-red-600"><strong>SW Error:</strong> {debugInfo.serviceWorkerError}</div>
        )}
        
        {debugInfo.backendError && (
          <div className="text-red-600"><strong>Backend Error:</strong> {debugInfo.backendError}</div>
        )}
        
        {debugInfo.vapidKeyError && (
          <div className="text-red-600"><strong>VAPID Error:</strong> {debugInfo.vapidKeyError}</div>
        )}
        
        {debugInfo.manualInitSuccess && (
          <div className="text-green-600"><strong>Manual Init:</strong> ✅ Success at {debugInfo.manualInitTime}</div>
        )}
        
        {debugInfo.manualInitError && (
          <div className="text-red-600"><strong>Manual Init Error:</strong> {debugInfo.manualInitError}</div>
        )}
      </div>
      
      <button
        onClick={initializeManually}
        className="mt-3 bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700"
      >
        Initialize Manually
      </button>
      
      <details className="mt-3">
        <summary className="cursor-pointer text-sm font-medium">Raw Debug Data</summary>
        <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
      </details>
    </div>
  );
}
