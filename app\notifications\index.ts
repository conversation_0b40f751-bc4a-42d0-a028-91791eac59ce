/**
 * Notification System Exports
 * Central export file for all notification-related components and utilities
 */

// Components
export { NotificationProvider, useNotifications } from './NotificationProvider';
export { NotificationPermission } from './NotificationPermission';
export { NotificationTest } from './NotificationTest';

// Service
export { notificationService } from './service';

// Types
export type {
  NotificationPermissionState,
  NotificationContextType,
  NotificationPayload,
  NotificationRequest,
  NotificationSubscription,
  PushSubscriptionData,
  PushSubscriptionKeys,
  NotificationAction,
  ServiceWorkerMessage,
  NotificationServiceConfig,
} from './types';

// Configuration
export {
  NOTIFICATION_CONFIG,
  API_ENDPOINTS,
  DEFAULT_NOTIFICATION_OPTIONS,
  SW_CONFIG,
  PERMISSION_CONFIG,
  DISPLAY_CONFIG,
  STORAGE_KEYS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
} from './config';
