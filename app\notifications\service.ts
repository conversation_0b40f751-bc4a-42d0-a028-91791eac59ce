/**
 * Notification Service
 * Handles all notification-related operations including permission requests,
 * service worker registration, subscription management, and server communication
 */

import {
  NotificationPermissionState,
  NotificationSubscription,
  PushSubscriptionData,
  ServiceWorkerMessage,
} from './types';
import {
  NOTIFICATION_CONFIG,
  API_ENDPOINTS,
  SW_CONFIG,
  STORAGE_KEYS,
  ERROR_MESSAGES,
  PERMISSION_CONFIG,
} from './config';

class NotificationService {
  private vapidPublicKey: string = '';
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null;

  /**
   * Check if push notifications are supported
   */
  isSupported(): boolean {
    return (
      'serviceWorker' in navigator &&
      'PushManager' in window &&
      'Notification' in window
    );
  }

  /**
   * Get current permission state
   */
  getPermissionState(): NotificationPermissionState {
    if (!this.isSupported()) {
      return {
        permission: 'denied',
        isSupported: false,
        isSubscribed: false,
        subscription: null,
      };
    }

    return {
      permission: Notification.permission,
      isSupported: true,
      isSubscribed: false, // Will be updated after checking subscription
      subscription: null,
    };
  }

  /**
   * Fetch VAPID public key from server
   */
  private async fetchVapidPublicKey(): Promise<string> {
    try {
      const response = await fetch(
        `${NOTIFICATION_CONFIG.serverUrl}${API_ENDPOINTS.VAPID_KEY}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      this.vapidPublicKey = data.public_key;
      return this.vapidPublicKey;
    } catch (error) {
      console.error('Failed to fetch VAPID public key:', error);
      throw new Error(ERROR_MESSAGES.SERVER_ERROR);
    }
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<ServiceWorkerRegistration> {
    console.log('[NotificationService] Attempting to register service worker at:', NOTIFICATION_CONFIG.swPath);

    try {
      const registration = await navigator.serviceWorker.register(
        NOTIFICATION_CONFIG.swPath,
        SW_CONFIG
      );

      console.log('[NotificationService] Service worker registration successful:', registration);

      // Wait for the service worker to be ready
      console.log('[NotificationService] Waiting for service worker to be ready...');
      await navigator.serviceWorker.ready;
      console.log('[NotificationService] Service worker is ready');

      this.serviceWorkerRegistration = registration;

      // Listen for service worker messages
      navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage);
      console.log('[NotificationService] Service worker message listener added');

      console.log('[NotificationService] Service worker registered successfully');
      return registration;
    } catch (error) {
      console.error('[NotificationService] Service worker registration failed:', error);
      throw new Error(ERROR_MESSAGES.SW_REGISTRATION_FAILED);
    }
  }

  /**
   * Handle messages from service worker
   */
  private handleServiceWorkerMessage = (event: MessageEvent<ServiceWorkerMessage>) => {
    console.log('Message from service worker:', event.data);
    
    switch (event.data.type) {
      case 'NOTIFICATION_CLICK':
        // Handle notification click
        this.onNotificationClick(event.data.data);
        break;
      case 'NOTIFICATION_CLOSE':
        // Handle notification close
        this.onNotificationClose(event.data.data);
        break;
      default:
        console.log('Unknown message type:', event.data.type);
    }
  };

  /**
   * Handle notification click events
   */
  private onNotificationClick(data: any) {
    console.log('Notification clicked:', data);
    // Emit custom event for components to listen to
    window.dispatchEvent(new CustomEvent('notificationClick', { detail: data }));
  }

  /**
   * Handle notification close events
   */
  private onNotificationClose(data: any) {
    console.log('Notification closed:', data);
    // Emit custom event for components to listen to
    window.dispatchEvent(new CustomEvent('notificationClose', { detail: data }));
  }

  /**
   * Request notification permission
   */
  async requestPermission(): Promise<boolean> {
    if (!this.isSupported()) {
      throw new Error(ERROR_MESSAGES.NOT_SUPPORTED);
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      throw new Error(ERROR_MESSAGES.PERMISSION_DENIED);
    }

    try {
      const permission = await Notification.requestPermission();
      
      // Store that we've requested permission
      localStorage.setItem(STORAGE_KEYS.PERMISSION_REQUESTED, 'true');
      localStorage.setItem(STORAGE_KEYS.LAST_PERMISSION_REQUEST, Date.now().toString());
      
      if (permission === 'granted') {
        return true;
      } else {
        throw new Error(ERROR_MESSAGES.PERMISSION_DENIED);
      }
    } catch (error) {
      console.error('Permission request failed:', error);
      throw error;
    }
  }

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    console.log('[NotificationService] Starting initialization...');

    if (!this.isSupported()) {
      console.error('[NotificationService] Push notifications not supported');
      throw new Error(ERROR_MESSAGES.NOT_SUPPORTED);
    }

    console.log('[NotificationService] Push notifications are supported');

    try {
      // Register service worker
      console.log('[NotificationService] Registering service worker...');
      await this.registerServiceWorker();
      console.log('[NotificationService] Service worker registered successfully');

      // Fetch VAPID public key
      console.log('[NotificationService] Fetching VAPID public key...');
      await this.fetchVapidPublicKey();
      console.log('[NotificationService] VAPID public key fetched:', this.vapidPublicKey.substring(0, 20) + '...');

      console.log('[NotificationService] Initialization completed successfully');
    } catch (error) {
      console.error('[NotificationService] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Create push subscription
   */
  async subscribe(): Promise<PushSubscription> {
    if (!this.serviceWorkerRegistration) {
      throw new Error('Service worker not registered');
    }

    if (!this.vapidPublicKey) {
      throw new Error(ERROR_MESSAGES.VAPID_KEY_MISSING);
    }

    try {
      const subscription = await this.serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey),
      });

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);
      
      // Store subscription locally
      localStorage.setItem(STORAGE_KEYS.SUBSCRIPTION_DATA, JSON.stringify(subscription));
      
      console.log('Push subscription created successfully');
      return subscription;
    } catch (error) {
      console.error('Failed to create push subscription:', error);
      throw new Error(ERROR_MESSAGES.SUBSCRIPTION_FAILED);
    }
  }

  /**
   * Send subscription data to server
   */
  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    const subscriptionData: NotificationSubscription = {
      subscription: {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')!),
        },
      },
      timestamp: new Date().toISOString(),
      user_agent: navigator.userAgent,
    };

    try {
      const response = await fetch(
        `${NOTIFICATION_CONFIG.serverUrl}${API_ENDPOINTS.SUBSCRIBE}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(subscriptionData),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Subscription sent to server:', result);
    } catch (error) {
      console.error('Failed to send subscription to server:', error);
      throw new Error(ERROR_MESSAGES.SERVER_ERROR);
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(): Promise<void> {
    if (!this.serviceWorkerRegistration) {
      return;
    }

    try {
      const subscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
      
      if (subscription) {
        // Unsubscribe from browser
        await subscription.unsubscribe();
        
        // Notify server
        await this.sendUnsubscribeToServer(subscription);
        
        // Clear local storage
        localStorage.removeItem(STORAGE_KEYS.SUBSCRIPTION_DATA);
        
        console.log('Successfully unsubscribed from push notifications');
      }
    } catch (error) {
      console.error('Failed to unsubscribe:', error);
      throw error;
    }
  }

  /**
   * Send unsubscribe request to server
   */
  private async sendUnsubscribeToServer(subscription: PushSubscription): Promise<void> {
    try {
      const response = await fetch(
        `${NOTIFICATION_CONFIG.serverUrl}${API_ENDPOINTS.UNSUBSCRIBE}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            subscription: {
              endpoint: subscription.endpoint,
              keys: {
                p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
                auth: this.arrayBufferToBase64(subscription.getKey('auth')!),
              },
            },
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to send unsubscribe to server:', error);
      // Don't throw here as local unsubscribe was successful
    }
  }

  /**
   * Check if user is currently subscribed
   */
  async isSubscribed(): Promise<boolean> {
    if (!this.serviceWorkerRegistration) {
      return false;
    }

    try {
      const subscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
      return subscription !== null;
    } catch (error) {
      console.error('Failed to check subscription status:', error);
      return false;
    }
  }

  /**
   * Get current subscription
   */
  async getSubscription(): Promise<PushSubscription | null> {
    if (!this.serviceWorkerRegistration) {
      return null;
    }

    try {
      return await this.serviceWorkerRegistration.pushManager.getSubscription();
    } catch (error) {
      console.error('Failed to get subscription:', error);
      return null;
    }
  }

  /**
   * Utility: Convert URL-safe base64 to Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);
    
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    
    return outputArray;
  }

  /**
   * Utility: Convert ArrayBuffer to base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    
    return window.btoa(binary);
  }

  /**
   * Check if permission was previously requested
   */
  wasPermissionRequested(): boolean {
    return localStorage.getItem(STORAGE_KEYS.PERMISSION_REQUESTED) === 'true';
  }

  /**
   * Check if we should auto-request permission
   */
  shouldAutoRequestPermission(): boolean {
    if (!PERMISSION_CONFIG.autoRequest) {
      return false;
    }

    if (this.wasPermissionRequested()) {
      return false;
    }

    if (Notification.permission !== 'default') {
      return false;
    }

    return true;
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
