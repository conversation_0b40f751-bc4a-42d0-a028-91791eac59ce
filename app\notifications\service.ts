/**
 * Notification Service
 * Handles all notification-related operations including permission requests,
 * service worker registration, subscription management, and server communication
 */

import {
  NotificationPermissionState,
  NotificationSubscription,
  PushSubscriptionData,
  ServiceWorkerMessage,
} from './types';
import {
  NOTIFICATION_CONFIG,
  API_ENDPOINTS,
  SW_CONFIG,
  STORAGE_KEYS,
  ERROR_MESSAGES,
  PERMISSION_CONFIG,
} from './config';

class NotificationService {
  private vapidPublicKey: string = '';
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null;

  /**
   * Check if push notifications are supported
   */
  isSupported(): boolean {
    const hasServiceWorker = 'serviceWorker' in navigator;
    const hasPushManager = 'PushManager' in window;
    const hasNotification = 'Notification' in window;

    console.log('[NotificationService] Support check:', {
      serviceWorker: hasServiceWorker,
      pushManager: hasPushManager,
      notification: hasNotification,
      isSecureContext: window.isSecureContext,
      protocol: window.location.protocol,
      userAgent: navigator.userAgent.substring(0, 100),
    });

    return hasServiceWorker && hasPushManager && hasNotification;
  }

  /**
   * Get current permission state
   */
  getPermissionState(): NotificationPermissionState {
    if (!this.isSupported()) {
      return {
        permission: 'denied',
        isSupported: false,
        isSubscribed: false,
        subscription: null,
      };
    }

    return {
      permission: Notification.permission,
      isSupported: true,
      isSubscribed: false, // Will be updated after checking subscription
      subscription: null,
    };
  }

  /**
   * Fetch VAPID public key from server
   */
  private async fetchVapidPublicKey(): Promise<string> {
    try {
      const response = await fetch(
        `${NOTIFICATION_CONFIG.serverUrl}${API_ENDPOINTS.VAPID_KEY}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      // Clean up any escaped characters in the VAPID key
      this.vapidPublicKey = data.public_key.replace(/\\_/g, '_');
      console.log('[NotificationService] Cleaned VAPID key:', this.vapidPublicKey);
      return this.vapidPublicKey;
    } catch (error) {
      console.error('Failed to fetch VAPID public key:', error);
      throw new Error(ERROR_MESSAGES.SERVER_ERROR);
    }
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<ServiceWorkerRegistration> {
    console.log('[NotificationService] Attempting to register service worker at:', NOTIFICATION_CONFIG.swPath);

    try {
      const registration = await navigator.serviceWorker.register(
        NOTIFICATION_CONFIG.swPath,
        SW_CONFIG
      );

      console.log('[NotificationService] Service worker registration successful:', registration);

      // Wait for the service worker to be ready
      console.log('[NotificationService] Waiting for service worker to be ready...');
      await navigator.serviceWorker.ready;
      console.log('[NotificationService] Service worker is ready');

      this.serviceWorkerRegistration = registration;

      // Listen for service worker messages
      navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage);
      console.log('[NotificationService] Service worker message listener added');

      console.log('[NotificationService] Service worker registered successfully');
      return registration;
    } catch (error) {
      console.error('[NotificationService] Service worker registration failed:', error);
      throw new Error(ERROR_MESSAGES.SW_REGISTRATION_FAILED);
    }
  }

  /**
   * Handle messages from service worker
   */
  private handleServiceWorkerMessage = (event: MessageEvent<ServiceWorkerMessage>) => {
    console.log('Message from service worker:', event.data);
    
    switch (event.data.type) {
      case 'NOTIFICATION_CLICK':
        // Handle notification click
        this.onNotificationClick(event.data.data);
        break;
      case 'NOTIFICATION_CLOSE':
        // Handle notification close
        this.onNotificationClose(event.data.data);
        break;
      default:
        console.log('Unknown message type:', event.data.type);
    }
  };

  /**
   * Handle notification click events
   */
  private onNotificationClick(data: any) {
    console.log('Notification clicked:', data);
    // Emit custom event for components to listen to
    window.dispatchEvent(new CustomEvent('notificationClick', { detail: data }));
  }

  /**
   * Handle notification close events
   */
  private onNotificationClose(data: any) {
    console.log('Notification closed:', data);
    // Emit custom event for components to listen to
    window.dispatchEvent(new CustomEvent('notificationClose', { detail: data }));
  }

  /**
   * Request notification permission
   */
  async requestPermission(): Promise<boolean> {
    if (!this.isSupported()) {
      throw new Error(ERROR_MESSAGES.NOT_SUPPORTED);
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      throw new Error(ERROR_MESSAGES.PERMISSION_DENIED);
    }

    try {
      const permission = await Notification.requestPermission();
      
      // Store that we've requested permission
      localStorage.setItem(STORAGE_KEYS.PERMISSION_REQUESTED, 'true');
      localStorage.setItem(STORAGE_KEYS.LAST_PERMISSION_REQUEST, Date.now().toString());
      
      if (permission === 'granted') {
        return true;
      } else {
        throw new Error(ERROR_MESSAGES.PERMISSION_DENIED);
      }
    } catch (error) {
      console.error('Permission request failed:', error);
      throw error;
    }
  }

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    console.log('[NotificationService] Starting initialization...');

    if (!this.isSupported()) {
      console.error('[NotificationService] Push notifications not supported');
      throw new Error(ERROR_MESSAGES.NOT_SUPPORTED);
    }

    console.log('[NotificationService] Push notifications are supported');

    try {
      // Register service worker
      console.log('[NotificationService] Registering service worker...');
      await this.registerServiceWorker();
      console.log('[NotificationService] Service worker registered successfully');

      // Fetch VAPID public key
      console.log('[NotificationService] Fetching VAPID public key...');
      await this.fetchVapidPublicKey();
      console.log('[NotificationService] VAPID public key fetched:', this.vapidPublicKey.substring(0, 20) + '...');

      console.log('[NotificationService] Initialization completed successfully');
    } catch (error) {
      console.error('[NotificationService] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Create push subscription
   */
  async subscribe(): Promise<PushSubscription> {
    console.log('[NotificationService] Starting subscription process...');

    if (!this.serviceWorkerRegistration) {
      console.error('[NotificationService] Service worker not registered');
      throw new Error('Service worker not registered');
    }

    if (!this.vapidPublicKey) {
      console.error('[NotificationService] VAPID public key missing');
      throw new Error(ERROR_MESSAGES.VAPID_KEY_MISSING);
    }

    console.log('[NotificationService] Service worker registration:', this.serviceWorkerRegistration);
    console.log('[NotificationService] VAPID public key length:', this.vapidPublicKey.length);

    try {
      // Check if already subscribed
      const existingSubscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
      if (existingSubscription) {
        console.log('[NotificationService] Existing subscription found, using it');
        await this.sendSubscriptionToServer(existingSubscription);
        localStorage.setItem(STORAGE_KEYS.SUBSCRIPTION_DATA, JSON.stringify(existingSubscription));
        return existingSubscription;
      }

      console.log('[NotificationService] Creating new push subscription...');

      // Check push manager support
      if (!this.serviceWorkerRegistration.pushManager) {
        throw new Error('Push manager not available');
      }

      // Convert VAPID key
      const applicationServerKey = this.urlBase64ToUint8Array(this.vapidPublicKey);
      console.log('[NotificationService] Application server key prepared, length:', applicationServerKey.length);

      console.log('[NotificationService] Attempting push subscription with options:', {
        userVisibleOnly: true,
        applicationServerKeyLength: applicationServerKey.length,
      });

      const subscription = await this.serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: applicationServerKey,
      });

      console.log('[NotificationService] Push subscription created:', subscription);

      // Send subscription to server
      console.log('[NotificationService] Sending subscription to server...');
      await this.sendSubscriptionToServer(subscription);

      // Store subscription locally
      localStorage.setItem(STORAGE_KEYS.SUBSCRIPTION_DATA, JSON.stringify(subscription));

      console.log('[NotificationService] Push subscription process completed successfully');
      return subscription;
    } catch (error) {
      console.error('[NotificationService] Failed to create push subscription:', error);

      // Type guard for error handling
      const err = error as Error;
      console.error('[NotificationService] Error details:', {
        name: err.name || 'Unknown',
        message: err.message || 'Unknown error',
        stack: err.stack || 'No stack trace'
      });

      // Provide more specific error messages
      let errorMessage = err.message || 'Unknown error';
      if (err.name === 'NotSupportedError') {
        errorMessage = 'Push notifications are not supported on this device/browser';
      } else if (err.name === 'NotAllowedError') {
        errorMessage = 'Push notifications permission was denied';
      } else if (err.name === 'AbortError') {
        errorMessage = 'Push subscription was aborted';
      } else if (errorMessage.includes('Registration failed')) {
        errorMessage = 'Push service registration failed - this may be due to network issues or browser restrictions';
      }

      throw new Error(`${ERROR_MESSAGES.SUBSCRIPTION_FAILED}: ${errorMessage}`);
    }
  }

  /**
   * Send subscription data to server
   */
  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    console.log('[NotificationService] Preparing subscription data for server...');

    const subscriptionData: NotificationSubscription = {
      subscription: {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')!),
        },
      },
      timestamp: new Date().toISOString(),
      user_agent: navigator.userAgent,
    };

    console.log('[NotificationService] Subscription data prepared:', {
      endpoint: subscriptionData.subscription.endpoint.substring(0, 50) + '...',
      p256dhLength: subscriptionData.subscription.keys.p256dh.length,
      authLength: subscriptionData.subscription.keys.auth.length,
    });

    try {
      const url = `${NOTIFICATION_CONFIG.serverUrl}${API_ENDPOINTS.SUBSCRIBE}`;
      console.log('[NotificationService] Sending subscription to:', url);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscriptionData),
      });

      console.log('[NotificationService] Server response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[NotificationService] Server error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('[NotificationService] Subscription sent to server successfully:', result);
    } catch (error) {
      console.error('[NotificationService] Failed to send subscription to server:', error);
      const err = error as Error;
      throw new Error(`${ERROR_MESSAGES.SERVER_ERROR}: ${err.message || 'Unknown error'}`);
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(): Promise<void> {
    if (!this.serviceWorkerRegistration) {
      return;
    }

    try {
      const subscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
      
      if (subscription) {
        // Unsubscribe from browser
        await subscription.unsubscribe();
        
        // Notify server
        await this.sendUnsubscribeToServer(subscription);
        
        // Clear local storage
        localStorage.removeItem(STORAGE_KEYS.SUBSCRIPTION_DATA);
        
        console.log('Successfully unsubscribed from push notifications');
      }
    } catch (error) {
      console.error('Failed to unsubscribe:', error);
      throw error;
    }
  }

  /**
   * Send unsubscribe request to server
   */
  private async sendUnsubscribeToServer(subscription: PushSubscription): Promise<void> {
    try {
      const response = await fetch(
        `${NOTIFICATION_CONFIG.serverUrl}${API_ENDPOINTS.UNSUBSCRIBE}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            subscription: {
              endpoint: subscription.endpoint,
              keys: {
                p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
                auth: this.arrayBufferToBase64(subscription.getKey('auth')!),
              },
            },
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to send unsubscribe to server:', error);
      // Don't throw here as local unsubscribe was successful
    }
  }

  /**
   * Check if user is currently subscribed
   */
  async isSubscribed(): Promise<boolean> {
    if (!this.serviceWorkerRegistration) {
      return false;
    }

    try {
      const subscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
      return subscription !== null;
    } catch (error) {
      console.error('Failed to check subscription status:', error);
      return false;
    }
  }

  /**
   * Get current subscription
   */
  async getSubscription(): Promise<PushSubscription | null> {
    if (!this.serviceWorkerRegistration) {
      return null;
    }

    try {
      return await this.serviceWorkerRegistration.pushManager.getSubscription();
    } catch (error) {
      console.error('Failed to get subscription:', error);
      return null;
    }
  }

  /**
   * Utility: Convert URL-safe base64 to Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    console.log('[NotificationService] Converting VAPID key to Uint8Array...');
    console.log('[NotificationService] Input base64 string length:', base64String.length);
    console.log('[NotificationService] Input base64 string:', base64String.substring(0, 20) + '...');

    try {
      const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
      const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
      console.log('[NotificationService] After padding and replacement:', base64.substring(0, 20) + '...');

      const rawData = window.atob(base64);
      console.log('[NotificationService] Decoded raw data length:', rawData.length);

      const outputArray = new Uint8Array(rawData.length);

      for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i);
      }

      console.log('[NotificationService] Converted to Uint8Array, length:', outputArray.length);
      return outputArray;
    } catch (error) {
      console.error('[NotificationService] VAPID key conversion failed:', error);
      throw new Error(`Invalid VAPID key format: ${error.message}`);
    }
  }

  /**
   * Utility: Convert ArrayBuffer to base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    
    return window.btoa(binary);
  }

  /**
   * Check if permission was previously requested
   */
  wasPermissionRequested(): boolean {
    return localStorage.getItem(STORAGE_KEYS.PERMISSION_REQUESTED) === 'true';
  }

  /**
   * Check if we should auto-request permission
   */
  shouldAutoRequestPermission(): boolean {
    if (!PERMISSION_CONFIG.autoRequest) {
      return false;
    }

    if (this.wasPermissionRequested()) {
      return false;
    }

    if (Notification.permission !== 'default') {
      return false;
    }

    return true;
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
