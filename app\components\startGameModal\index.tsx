import {
  StartGameModalProps,
  UpdateInGameNameResponse,
} from "@/app/types/CommonComponent.types";
import Link from "next/link";
import UpdateInGameNameForm from "../updateInGameNameForm";
import { useState, useEffect } from "react";
import { toast } from "react-toastify";
const StartGameModal: React.FC<StartGameModalProps> = ({
  game_link,
  room_password,
  room_id,
  bookingId,
  tournamentName,
  inGameName,
  showUpdateInGameNameForm = false,
  onSuccess,
  createdBy,
}) => {
  const [updatedInGameName, setUpdatedInGameName] = useState(inGameName);
  const [updatedRoomId, setUpdatedRoomId] = useState(room_id);
  const [updatedRoomPassword, setUpdatedRoomPassword] = useState(room_password);
  const [updatedGameLink, setUpdatedGameLink] = useState(game_link);

  // Sync local state with props when they change (Jroori hai refresh functionality ke liye)
  useEffect(() => {
    setUpdatedRoomId(room_id);
  }, [room_id]);

  useEffect(() => {
    setUpdatedRoomPassword(room_password);
  }, [room_password]);

  useEffect(() => {
    setUpdatedGameLink(game_link);
  }, [game_link]);

  useEffect(() => {
    setUpdatedInGameName(inGameName);
  }, [inGameName]);

  const onInGameNameUpdate = (data: UpdateInGameNameResponse) => {
    if (onSuccess && typeof onSuccess === "function") {
      onSuccess();
    }
    setUpdatedInGameName(data.in_game_name);
    setUpdatedRoomId(data.room_id);
    setUpdatedRoomPassword(data.room_password);
    setUpdatedGameLink(data.game_link);
  };
  return (
    <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white p-6">
      {/* Header with title */}
      <div className="mb-6">
        <h3 className="text-3xl font-bold text-white text-center">
          Game Joining Details
        </h3>
      </div>

      {/* Main content layout */}
      <div className="flex gap-6">
        {/* Host ki Image */}
        {createdBy && (
          <div className="flex-shrink-0 w-[160px] flex flex-col justify-center items-center">
            <div className="relative mb-2">
              <img
                src={createdBy.image || "/icons/youtube.svg"}
                alt={createdBy.name}
                className="w-[140px] h-[140px] rounded-lg object-cover border-2 border-[#c9ff88]"
              />
              <div className="absolute -bottom-2 left-0 right-0 mx-auto w-max bg-[#141517] px-2 py-1 rounded-md border border-[#c9ff88]">
                <span className="text-xs text-[#c9ff88] font-medium">Creator</span>
              </div>
            </div>
            <Link
              href={createdBy.youtube_link || ""}
              target="_blank"
              className="flex flex-col items-center w-full bg-[#c9ff88] text-[#070b28] px-3 py-1 rounded-[10px] text-sm font-semibold hover:bg-[#a7f04e] transition-colors mt-2"
            >
              <span className="text-center leading-tight">{createdBy.name}</span>
            </Link>
          </div>
        )}


        <div className="flex-1 mx-3 flex flex-col space-y-2">
          {!updatedInGameName && showUpdateInGameNameForm && (
            <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-2">
              <UpdateInGameNameForm
                bookingId={bookingId as string}
                onSuccess={onInGameNameUpdate}
                tournamentName={tournamentName as string}
              />
            </div>
          )}

          {/* Game Details Section */}
          <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
            {showUpdateInGameNameForm && (
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
                <div className="flex items-center justify-between flex-1">
                  <span className="text-white text-sm uppercase tracking-wider opacity-70">In Game Name:</span>
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-white text-base">
                      {updatedInGameName}
                    </span>
                    {updatedInGameName && (
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(updatedInGameName);
                          toast.success("In Game Name copied to clipboard", {
                            position: "top-right",
                            autoClose: 2000,
                            hideProgressBar: false,
                            closeOnClick: true,
                            pauseOnHover: true,
                            draggable: true,
                          });
                        }}
                        className="p-1 bg-[#2a2c2e] hover:bg-[#3a3c3e] rounded-md transition-colors border border-[#c9ff88]"
                        title="Copy In Game Name"
                      >
                        <img
                          src="/icons/copy-icon.svg"
                          alt="Copy"
                          className="h-4 w-4 invert brightness-100"
                        />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-2 mb-3">
              <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
              <div className="flex items-center justify-between flex-1">
                <span className="text-white text-sm uppercase tracking-wider opacity-70">Room ID:</span>
                <div className="flex items-center gap-2">
                  <span className="font-bold text-white text-base">
                    {updatedRoomId || <span className="text-yellow-400">Please refresh when it&apos;s tournament time</span>}
                  </span>
                  {updatedRoomId && (
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(updatedRoomId);
                        toast.success("Room ID copied to clipboard", {
                          position: "top-right",
                          autoClose: 2000,
                          hideProgressBar: false,
                          closeOnClick: true,
                          pauseOnHover: true,
                          draggable: true,
                        });
                      }}
                      className="p-1 bg-[#2a2c2e] hover:bg-[#3a3c3e] rounded-md transition-colors border border-[#c9ff88]"
                      title="Copy Room ID"
                    >
                      <img
                        src="/icons/copy-icon.svg"
                        alt="Copy"
                        className="h-4 w-4 invert brightness-100"
                      />
                    </button>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-2 h-6 bg-[#c9ff88] rounded-sm"></div>
              <div className="flex items-center justify-between flex-1">
                <span className="text-white text-sm uppercase tracking-wider opacity-70">Room Password:</span>
                <div className="flex items-center gap-2">
                  <span className="font-bold text-white text-base">
                    {updatedRoomPassword || ""}
                  </span>
                  {updatedRoomPassword && (
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(updatedRoomPassword);
                        toast.success("Room Password copied to clipboard", {
                          position: "top-right",
                          autoClose: 2000,
                          hideProgressBar: false,
                          closeOnClick: true,
                          pauseOnHover: true,
                          draggable: true,
                        });
                      }}
                      className="p-1 bg-[#2a2c2e] hover:bg-[#3a3c3e] rounded-md transition-colors border border-[#c9ff88]"
                      title="Copy Room Password"
                    >
                      <img
                        src="/icons/copy-icon.svg"
                        alt="Copy"
                        className="h-4 w-4 invert brightness-100"
                      />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Notes Section */}
          <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
            <div className="flex items-center mb-2">
              <div className="w-2 h-5 bg-[#c9ff88] rounded-sm mr-2"></div>
              <p className="text-white text-xs uppercase tracking-wider opacity-100">
                <span className="font-semibold normal-case opacity-100 text-red-500">Notes:</span> Joining details will be available before the start of the tournament.
              </p>
            </div>
          </div>

          {/* Action Button */}
          <div className="mt-4">
            <Link
              href={updatedGameLink ?? ""}
              target="_blank"
              className={`flex justify-center w-full bg-red-600 px-6 py-2 rounded-[10px] text-base font-semibold text-white shadow-sm hover:bg-red-500 transition-colors ${
                !updatedGameLink ? "cursor-not-allowed opacity-50" : ""
              }`}
              onClick={(e) => {
                if (!updatedGameLink) {
                  e.preventDefault();
                }
              }}
            >
              Click to Watch Live
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StartGameModal;
